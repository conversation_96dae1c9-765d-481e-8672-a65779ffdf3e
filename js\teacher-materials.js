// Import Firebase modules
import { collection, getDocs, doc, updateDoc, arrayUnion, deleteDoc, query, where } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

// Use the globally initialized Firebase instance
let db;

// Wait for the Firebase instance to be available
function getFirestoreInstance() {
    return new Promise((resolve, reject) => {
        // Check if db is already available in the window object
        if (window.db) {
            console.log("Using existing Firestore instance from window object");
            resolve(window.db);
            return;
        }

        // If not available, wait for it with a timeout
        let attempts = 0;
        const maxAttempts = 10;
        const checkInterval = setInterval(() => {
            attempts++;
            if (window.db) {
                clearInterval(checkInterval);
                console.log("Firestore instance found after waiting");
                resolve(window.db);
            } else if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                reject(new Error("Firestore instance not available after maximum attempts"));
            }
        }, 300);
    });
}

// Initialize db when the module loads
getFirestoreInstance()
    .then(firestoreInstance => {
        db = firestoreInstance;
        console.log("Firestore instance set in teacher-materials.js");
    })
    .catch(error => {
        console.error("Error getting Firestore instance:", error);
    });

// Function to group classrooms based on the selected option
function groupClassrooms(classrooms, groupOption) {
    if (groupOption === 'none') {
        return { 'All Classrooms': classrooms };
    }

    const groups = {};

    classrooms.forEach(classroom => {
        let groupKey;

        switch (groupOption) {
            case 'subject':
                groupKey = classroom.subjectName || 'Unknown Subject';
                break;

            case 'grade':
                groupKey = classroom.gradeLevel || 'Unknown Grade';
                break;

            case 'course':
                groupKey = classroom.course || 'Unknown Course';
                break;

            default:
                groupKey = 'All Classrooms';
        }

        if (!groups[groupKey]) {
            groups[groupKey] = [];
        }
        groups[groupKey].push(classroom);
    });

    return groups;
}

// Function to sort classrooms based on the selected option
function sortClassrooms(a, b, sortOption) {
    switch (sortOption) {
        case 'name-asc':
            const nameA = (a.sectionName ? `${a.subjectName} - ${a.sectionName}` : (a.subjectName || a.name || 'Unnamed Class')).toLowerCase();
            const nameB = (b.sectionName ? `${b.subjectName} - ${b.sectionName}` : (b.subjectName || b.name || 'Unnamed Class')).toLowerCase();
            return nameA.localeCompare(nameB);

        case 'name-desc':
            const nameA2 = (a.sectionName ? `${a.subjectName} - ${a.sectionName}` : (a.subjectName || a.name || 'Unnamed Class')).toLowerCase();
            const nameB2 = (b.sectionName ? `${b.subjectName} - ${b.sectionName}` : (b.subjectName || b.name || 'Unnamed Class')).toLowerCase();
            return nameB2.localeCompare(nameA2);

        case 'date-desc':
            const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt) || new Date(0);
            const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt) || new Date(0);
            return dateB.getTime() - dateA.getTime();

        case 'date-asc':
            const dateA2 = a.createdAt?.toDate?.() || new Date(a.createdAt) || new Date(0);
            const dateB2 = b.createdAt?.toDate?.() || new Date(b.createdAt) || new Date(0);
            return dateA2.getTime() - dateB2.getTime();

        case 'grade-asc':
            const gradeA = parseInt((a.gradeLevel || '').replace('Grade ', '')) || 0;
            const gradeB = parseInt((b.gradeLevel || '').replace('Grade ', '')) || 0;
            return gradeA - gradeB;

        case 'grade-desc':
            const gradeA2 = parseInt((a.gradeLevel || '').replace('Grade ', '')) || 0;
            const gradeB2 = parseInt((b.gradeLevel || '').replace('Grade ', '')) || 0;
            return gradeB2 - gradeA2;

        case 'course-asc':
            const courseA = (a.course || '').toLowerCase();
            const courseB = (b.course || '').toLowerCase();
            return courseA.localeCompare(courseB);

        case 'course-desc':
            const courseA2 = (a.course || '').toLowerCase();
            const courseB2 = (b.course || '').toLowerCase();
            return courseB2.localeCompare(courseA2);

        case 'materials-desc':
            const materialsA = (a.materials || []).length;
            const materialsB = (b.materials || []).length;
            return materialsB - materialsA;

        case 'materials-asc':
            const materialsA2 = (a.materials || []).length;
            const materialsB2 = (b.materials || []).length;
            return materialsA2 - materialsB2;

        default:
            return 0;
    }
}

// Function to load quiz notifications for specific classrooms
async function loadQuizNotificationsForClassrooms(classroomIds) {
    try {
        if (!classroomIds || classroomIds.length === 0) {
            return {};
        }

        const notificationsRef = collection(db, "QuizNotifications");
        const notificationQuery = query(
            notificationsRef,
            where("classroomId", "in", classroomIds)
        );
        const notificationSnapshot = await getDocs(notificationQuery);

        const notificationsByClassroom = {};

        notificationSnapshot.forEach((doc) => {
            const notification = { id: doc.id, ...doc.data() };
            const classroomId = notification.classroomId;

            if (!notificationsByClassroom[classroomId]) {
                notificationsByClassroom[classroomId] = [];
            }
            notificationsByClassroom[classroomId].push(notification);
        });

        // Sort notifications by sent date (newest first) for each classroom
        Object.keys(notificationsByClassroom).forEach(classroomId => {
            notificationsByClassroom[classroomId].sort((a, b) => {
                const aTime = a.sentAt?.toDate?.() || new Date(a.sentAt);
                const bTime = b.sentAt?.toDate?.() || new Date(b.sentAt);
                return bTime.getTime() - aTime.getTime();
            });
        });

        return notificationsByClassroom;
    } catch (error) {
        console.error("Error loading quiz notifications:", error);
        return {};
    }
}

// Function to format time ago
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return date.toLocaleDateString();
}

// Function to remove a teacher notification
async function removeTeacherNotification(notificationId) {
    try {
        // Confirm with user before removing
        if (!confirm('Are you sure you want to remove this notification?')) {
            return;
        }

        // Import deleteDoc function
        const { deleteDoc, doc } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

        // Delete the notification from Firestore
        const notificationRef = doc(db, "QuizNotifications", notificationId);
        await deleteDoc(notificationRef);

        // Show success message
        console.log('Notification removed successfully');

        // Reload classrooms to update the display
        displayClassrooms();

    } catch (error) {
        console.error("Error removing notification:", error);
        alert('Error removing notification. Please try again.');
    }
}

// Function to display classrooms in a list format
async function displayClassrooms() {
    console.log('displayClassrooms function called');
    try {
        const teacherData = JSON.parse(localStorage.getItem('teacherData'));
        if (!teacherData || !teacherData.email) {
            console.error('Teacher data not found in localStorage');
            return;
        }

        const teacherEmail = teacherData.email;
        console.log('Teacher email:', teacherEmail);

        const materialsList = document.getElementById('materialsList');
        if (!materialsList) {
            console.error('materialsList element not found');
            return;
        }

        console.log('materialsList element found, showing loading state');
        // Show loading state
        materialsList.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div></div>';

        // Make sure we have a Firestore instance
        if (!db) {
            console.log("Waiting for Firestore instance...");
            db = await getFirestoreInstance();
            console.log("Firestore instance obtained for displayClassrooms");
        }

        // Get all classrooms from Firestore
        const classroomsRef = collection(db, "Classrooms");
        const querySnapshot = await getDocs(classroomsRef);

        if (querySnapshot.empty) {
            materialsList.innerHTML = '<div class="alert alert-info">No classrooms found.</div>';
            return;
        }

        // Filter classrooms created by this teacher
        const teacherClassrooms = [];
        querySnapshot.forEach((doc) => {
            const classroom = doc.data();
            // Include the document ID with the data
            const classroomWithId = { id: doc.id, ...classroom };

            // Only include classrooms created by this teacher
            if (classroom.createdBy === teacherEmail) {
                teacherClassrooms.push(classroomWithId);
            }
        });

        // Load notifications for all teacher's classrooms
        const classroomIds = teacherClassrooms.map(classroom => classroom.id);
        const notificationsByClassroom = await loadQuizNotificationsForClassrooms(classroomIds);

        if (teacherClassrooms.length === 0) {
            materialsList.innerHTML = '<div class="alert alert-info">You have not created any classrooms yet.</div>';
            return;
        }

        let html = '';

        // Get grouping and sorting options
        const groupOption = document.getElementById('materials-group-select')?.value ||
                           localStorage.getItem('materialsGroupPreference') || 'none';
        const sortOption = document.getElementById('materials-sort-select')?.value ||
                          localStorage.getItem('materialsSortPreference') || 'name-asc';

        // Sort classrooms first
        teacherClassrooms.sort((a, b) => sortClassrooms(a, b, sortOption));

        // Group classrooms
        const groupedClassrooms = groupClassrooms(teacherClassrooms, groupOption);

        // Log the classrooms for debugging
        console.log('Teacher created classrooms:', teacherClassrooms);
        console.log(`Found ${teacherClassrooms.length} classroom(s) for teacher: ${teacherEmail}`);
        console.log('Grouped classrooms:', groupedClassrooms);

        // Render grouped classrooms
        Object.keys(groupedClassrooms).forEach(groupName => {
            const classroomsInGroup = groupedClassrooms[groupName];

            // Add group header if grouping is enabled
            if (groupOption !== 'none') {
                html += `
                    <div class="group-header">
                        <div class="group-info">
                            ${groupName}
                            <span class="group-count">${classroomsInGroup.length} classroom${classroomsInGroup.length !== 1 ? 's' : ''}</span>
                        </div>
                        <button class="group-minimize-btn" type="button">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                `;
            }

            html += '<div class="group-content">';

            classroomsInGroup.forEach((classroom) => {
                // Log classroom data for debugging
                console.log('Processing classroom:', classroom);

                // Extract classroom data safely with better fallbacks
                const subjectName = classroom.subjectName || classroom.name || 'Unnamed Class';
                const sectionName = classroom.sectionName || '';
                const enrollCode = classroom.enrollCode || 'No code';

                // Create display name combining subject and section
                const displayName = sectionName ? `${subjectName} - ${sectionName}` : subjectName;

                // Improved quarter extraction - check multiple possible sources
                let quarter = null; // no default, will hide if not found
                if (classroom.quarter) {
                    quarter = classroom.quarter;
                } else if (classroom.details) {
                    // Try to extract quarter from details
                    const quarterMatch = classroom.details.match(/Quarter\s*(\d+)/i);
                    if (quarterMatch) {
                        quarter = quarterMatch[1];
                    }
                }

                // Improved grade level formatting
                let gradeLevel = 'N/A';
                if (classroom.gradeLevel) {
                    gradeLevel = classroom.gradeLevel;
                    // Remove "Grade " prefix if it exists for cleaner display
                    if (typeof gradeLevel === 'string' && gradeLevel.toLowerCase().startsWith('grade ')) {
                        gradeLevel = gradeLevel.substring(6); // Remove "Grade " (6 characters)
                    }
                }

                const course = classroom.course || 'N/A';
                const details = classroom.details || '';

                // Get creator information
                const createdBy = classroom.createdBy || 'Unknown';

                // Count materials in this classroom
                const materialsCount = classroom.materials ? classroom.materials.length : 0;

                // Get notifications for this classroom
                const classroomNotifications = notificationsByClassroom[classroom.id] || [];
                const unreadNotifications = classroomNotifications.filter(n => !n.isRead);

                // Generate notifications HTML
                let notificationsHtml = '';
                if (classroomNotifications.length > 0) {
                    const displayNotifications = classroomNotifications.slice(0, 3); // Show only latest 3
                    notificationsHtml = `
                        <div class="classroom-notifications mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted font-weight-bold">
                                    <i class="fas fa-bell text-warning"></i> Quiz Notifications
                                    ${unreadNotifications.length > 0 ? `<span class="badge badge-warning ml-1">${unreadNotifications.length} new</span>` : ''}
                                </small>
                                ${classroomNotifications.length > 3 ? `<small class="text-muted">${classroomNotifications.length - 3} more...</small>` : ''}
                            </div>
                            <div class="notifications-list">
                                ${displayNotifications.map(notification => {
                                    const sentDate = notification.sentAt?.toDate?.() || new Date(notification.sentAt);
                                    const updatedDate = notification.updatedAt?.toDate?.() || null;
                                    const timeAgo = getTimeAgo(sentDate);
                                    const isUpdated = updatedDate && updatedDate.getTime() !== sentDate.getTime();
                                    return `
                                        <div class="notification-item p-2 mb-2 border rounded ${notification.isRead ? 'bg-light' : 'bg-warning-light'}" style="font-size: 0.85rem;">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-1">
                                                        <span class="badge badge-primary badge-sm mr-1" style="font-size: 0.7rem;">${notification.label}</span>
                                                        ${!notification.isRead ? '<span class="badge badge-warning badge-sm">New</span>' : ''}
                                                        ${isUpdated ? '<span class="badge badge-info badge-sm ml-1" style="font-size: 0.7rem;">Updated</span>' : ''}
                                                    </div>
                                                    <div class="font-weight-bold text-dark mb-1">${notification.quizTitle}</div>
                                                    <div class="text-muted mb-1">Code: ${notification.quizCode}</div>
                                                    ${notification.message ? `<div class="text-dark mb-1">${notification.message}</div>` : ''}
                                                    <small class="text-muted">
                                                        From: ${notification.sentByName} • ${timeAgo}
                                                        ${isUpdated ? ` • Updated: ${getTimeAgo(updatedDate)}` : ''}
                                                    </small>
                                                </div>
                                                <div class="ml-2">
                                                    <button class="btn btn-outline-danger btn-sm remove-teacher-notification-btn" data-notification-id="${notification.id}" title="Remove notification" style="font-size: 0.7rem; padding: 2px 6px;">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `;
                }

                html += `
                    <div class="classroom-item mb-4 bg-white rounded shadow-sm border">
                        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                            <h5 class="m-0 font-weight-bold text-dark">${displayName}</h5>
                            <small class="text-muted">Code: ${enrollCode}</small>
                        </div>
                        <div class="p-3">
                            ${notificationsHtml}
                            <div class="row mb-2">
                                ${quarter ? `
                                    <div class="col-md-6">
                                        <small class="text-muted">Quarter:</small>
                                        <div class="font-weight-bold">Quarter ${quarter}</div>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Grade & Course:</small>
                                        <div class="font-weight-bold">Grade ${gradeLevel} - ${course}</div>
                                    </div>
                                ` : `
                                    <div class="col-12">
                                        <small class="text-muted">Grade & Course:</small>
                                        <div class="font-weight-bold">Grade ${gradeLevel} - ${course}</div>
                                    </div>
                                `}
                            </div>
                            ${details ? `
                                <div class="mb-2">
                                    <small class="text-muted">Details:</small>
                                    <div class="text-dark">${details}</div>
                                </div>
                            ` : ''}
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <small class="text-muted">Created by:</small>
                                    <div class="text-dark">${createdBy}</div>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">Materials:</small>
                                    <div class="text-dark">${materialsCount} item${materialsCount !== 1 ? 's' : ''}</div>
                                </div>
                            </div>
                            <button onclick="window.showClassroomMaterials(${JSON.stringify(classroom).replace(/"/g, '&quot;')})" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-book"></i> View Materials
                            </button>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        });

        materialsList.innerHTML = html;

        // Add event listeners to newly created minimize buttons and restore states
        setTimeout(() => {
            // Add event listeners to minimize buttons
            document.querySelectorAll('.group-minimize-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const header = button.closest('.group-header');
                    if (!header) return;

                    const content = header.nextElementSibling;
                    if (content && content.classList.contains('group-content')) {
                        const isCollapsed = content.classList.contains('collapsed');

                        if (isCollapsed) {
                            content.classList.remove('collapsed');
                            header.classList.remove('collapsed');
                            button.innerHTML = '<i class="fas fa-chevron-down"></i>';
                        } else {
                            content.classList.add('collapsed');
                            header.classList.add('collapsed');
                            button.innerHTML = '<i class="fas fa-chevron-right"></i>';
                        }

                        // Save state to localStorage
                        const groupName = header.textContent.trim().split('\n')[0].trim();
                        const storageKey = `groupCollapsed_materials_${groupName}`;
                        localStorage.setItem(storageKey, !isCollapsed);
                    }
                });
            });

            // Restore group collapse states after content is loaded
            if (typeof window.restoreGroupStates === 'function') {
                window.restoreGroupStates();
            }
        }, 100);

        // Add event listeners to view materials buttons
        document.querySelectorAll('.btn-outline-primary').forEach(button => {
            button.addEventListener('click', function(e) {
                // Buttons now use onclick handlers, so no additional handling needed
                // This is just for any additional functionality we might want to add later
            });
        });

        // Add event listeners for remove notification buttons
        document.querySelectorAll('.remove-teacher-notification-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const notificationId = this.getAttribute('data-notification-id');
                removeTeacherNotification(notificationId);
            });
        });
    } catch (error) {
        console.error("Error fetching classrooms:", error);
        document.getElementById('materialsList').innerHTML =
            '<p class="text-center text-danger">Error loading classrooms. Please try again later.</p>';
    }
}

// Function to open the post material modal
function openPostMaterialModal(classroomId, classroomName) {
    // Create modal if it doesn't exist
    if (!document.getElementById('postMaterialModal')) {
        const modalHtml = `
            <div class="modal fade" id="postMaterialModal" tabindex="-1" role="dialog" aria-labelledby="postMaterialModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="postMaterialModalLabel">Post Material to <span id="modal-classroom-name"></span></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="post-material-form">
                                <input type="hidden" id="classroom-id-input">
                                <div class="form-group">
                                    <label for="material-title">Material Title</label>
                                    <input type="text" class="form-control" id="material-title" required>
                                </div>
                                <div class="form-group">
                                    <label for="material-description">Description</label>
                                    <textarea class="form-control" id="material-description" rows="3"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="material-link">Link (optional)</label>
                                    <input type="url" class="form-control" id="material-link">
                                </div>
                                <div class="form-group">
                                    <label for="material-link-name">Link Text (optional)</label>
                                    <input type="text" class="form-control" id="material-link-name" placeholder="e.g., 'View Document'">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="submit-material-btn">Post Material</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to the document
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add event listener to submit button
        document.getElementById('submit-material-btn').addEventListener('click', postMaterial);
    }

    // Set classroom info in the modal
    document.getElementById('modal-classroom-name').textContent = classroomName;
    document.getElementById('classroom-id-input').value = classroomId;

    // Show the modal
    $('#postMaterialModal').modal('show');
}

// Function to post material to a classroom
async function postMaterial() {
    const classroomId = document.getElementById('classroom-id-input').value;
    const title = document.getElementById('material-title').value;
    const description = document.getElementById('material-description').value;
    const link = document.getElementById('material-link').value;
    const linkName = document.getElementById('material-link-name').value || 'Open Link';

    if (!title) {
        alert('Please enter a material title');
        return;
    }

    try {
        const teacherData = JSON.parse(localStorage.getItem('teacherData'));
        const teacherName = `${teacherData.firstName} ${teacherData.lastName}`;

        const material = {
            title: title,
            description: description,
            link: link,
            linkName: linkName,
            timestamp: new Date().toISOString(),
            postedBy: teacherName,
            postedByEmail: teacherData.email
        };

        // Update the classroom document with the new material
        const classroomRef = doc(db, "Classrooms", classroomId);
        await updateDoc(classroomRef, {
            materials: arrayUnion(material)
        });

        // Close the modal
        $('#postMaterialModal').modal('hide');

        // Reset the form
        document.getElementById('post-material-form').reset();

        // Show success message
        alert('Material posted successfully!');

        // Close the modal
        const modal = document.getElementById('materialModal');
        if (modal) {
            $(modal).modal('hide');
        }

        // Refresh materials if we're currently viewing a classroom
        if (window.currentClassroomId && window.loadClassroomMaterials) {
            window.loadClassroomMaterials(window.currentClassroomId);
        }
    } catch (error) {
        console.error("Error posting material:", error);
        alert('Failed to post material: ' + error.message);
    }
}

// Make displayClassrooms function available globally
window.displayClassrooms = displayClassrooms;

// Listen for the refresh event
window.addEventListener('refreshClassrooms', function() {
    console.log('Refresh classrooms event received in teacher-materials.js');
    displayClassrooms();
});

// Consolidated DOMContentLoaded event listener
document.addEventListener('DOMContentLoaded', function() {
    console.log('Teacher materials script loaded');

    // Initialize display
    displayClassrooms();

    // Add event listeners for sort and group dropdowns
    const sortSelect = document.getElementById('materials-sort-select');
    const groupSelect = document.getElementById('materials-group-select');

    if (sortSelect) {
        // Load saved sort preference
        const savedSort = localStorage.getItem('materialsSortPreference');
        if (savedSort) {
            sortSelect.value = savedSort;
        }

        sortSelect.addEventListener('change', function() {
            console.log('Sort option changed to:', this.value);
            // Save sort preference
            localStorage.setItem('materialsSortPreference', this.value);
            displayClassrooms();
        });
    } else {
        console.warn('Materials sort select element not found');
    }

    if (groupSelect) {
        // Load saved group preference
        const savedGroup = localStorage.getItem('materialsGroupPreference');
        if (savedGroup) {
            groupSelect.value = savedGroup;
        }

        groupSelect.addEventListener('change', function() {
            console.log('Group option changed to:', this.value);
            // Save group preference
            localStorage.setItem('materialsGroupPreference', this.value);
            displayClassrooms();
        });
    } else {
        console.warn('Materials group select element not found');
    }
});
