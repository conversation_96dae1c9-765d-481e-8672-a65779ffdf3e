<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Dashboard</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/BINHSLogo.ico">

    <!-- Custom fonts for this template-->
    <link href="assets/admin/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="assets/admin/css/sb-admin-2.min.css" rel="stylesheet">

    <link href="assets/admin/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="assets/admin/css/sb-admin-2.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">





</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="#" id="dashboard-icon">
                <div class="sidebar-brand-icon">
                    <img src="assets/img/BINHSLogo.ico" alt="BINHS Logo" style="width: 30px; height: 30px;">
                </div>
                <div class="sidebar-brand-text mx-3">Dashboard</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->

            <!-- Heading -->
            <!--<div class="sidebar-heading">
                Interface
            </div> -->

            <!-- Nav Item - Pages Collapse Menu -->
            <li class="nav-item">
                <button class="nav-link" id="view-modules-btn" style="background: none; border: none; width: 100%; text-align: left;">
                    <i class="fas fa-book-open"></i>
                    <span class="nav-text">My Class</span>
                </button>
            </li>


            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">



            <li class="nav-item">
                <button class="nav-link" id="quizzes-exams-btn" style="background: none; border: none; width: 100%; text-align: left;">
                    <i class="fa-solid fa-pen-to-square"></i>
                    <span class="nav-text" id="quizzes-exams">Quizzes and Exams</span>
                    <span id="notification-badge" class="badge badge-warning ml-2" style="display: none;">0</span>
                </button>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            </li>
            <!-- Nav Item - Pages Collapse Menu -->



            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>


            <!-- Sidebar Message
             <div class="sidebar-card d-none d-lg-flex">
                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">
                <p class="text-center mb-2"><strong>SB Admin Pro</strong> is packed with premium features, components, and more!</p>
                <a class="btn btn-success btn-sm" href="https://startbootstrap.com/theme/sb-admin-pro">Upgrade to Pro!</a>
            </div>-->


        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <li class="nav-item" id="sidebarToggle">
                        <a class="nav-link" href="#">
                            <i class="fas fa-bars"></i>
                        </a>
                    </li>

                    <!-- Topbar Search -->
                    <form
                        class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">

                    </form>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">



                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-lg-inline text-gray-600 small" id="userName">Student</span>
                                <i class="fas fa-cog fa-fw text-gray-400"></i>
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in" aria-labelledby="userDropdown">
                                <div class="dropdown-header text-center">
                                    <div class="profile-info">
                                        <span id="profileName" class="font-weight-bold"></span>
                                        <div class="small text-gray-500" id="profileEmail"></div>
                                    </div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#profileModal">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    View Profile
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>
                    </ul>
                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid" id="modules-container">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4" id="main-heading">
                        <h1 class="h3 mb-0 text-gray-800">Welcome Student!</h1>
                    </div>

                    <!-- Classroom Materials Section (Initially Hidden) -->
                    <div id="classroom-materials-section" style="display: none;">
                        <div class="d-sm-flex align-items-center justify-content-between mb-4">
                            <h1 class="h3 mb-0 text-gray-800" id="classroom-title">Materials</h1>
                            <button id="back-to-classrooms" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Classrooms
                            </button>
                        </div>

                        <!-- Classroom Info Card -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3" id="classroom-header">
                                <h6 class="m-0 font-weight-bold text-white">Classroom Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Subject:</strong> <span id="subject-name"></span></p>
                                        <p><strong>Details:</strong> <span id="classroom-details"></span></p>
                                        <p><strong>Created By:</strong> <span id="created-by"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Grade Level:</strong> <span id="grade-level"></span></p>
                                        <p><strong>Section:</strong> <span id="section-name"></span></p>
                                        <p><strong>Course/Strand:</strong> <span id="course-strand"></span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Materials Content -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Learning Materials</h6>
                            </div>
                            <div class="card-body">
                                <div id="materials-container">
                                    <p class="text-muted">Loading materials...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Classroom List Section -->
                    <div id="main-classroom-section">
                        <button class="btn mb-3" id="join-classroom-btn">+ Join Class</button>

                    <!-- Join Classroom Card -->
                    <div id="join-classroom-card" class="card shadow p-4 hidden">
                        <div class="card-body">
                            <h5 class="text-primary text-uppercase mb-3">Join</h5>
                            <div id="join-classroom-form">
                                <label for="code">Enter Code:</label>
                                <input type="text" id="code" class="form-control mb-2" placeholder="Enter code"
                                    required>

                                <div class="d-flex flex-column flex-md-row gap-2">
                                    <button class="btn btn-primary w-100 w-md-auto" id="submit-code">Join</button>
                                    <button class="btn btn-secondary w-100 w-md-auto"
                                        id="cancel-classroom">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Class Code Notifications Section -->
                    <div id="class-code-notifications-section" class="mb-4" style="display: none;">
                        <div class="d-sm-flex align-items-center justify-content-between mb-3">
                            <h4 class="h5 mb-0 text-gray-800">
                                <i class="fas fa-key text-success"></i> Class Code Invitations
                            </h4>
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-secondary mr-2" id="clear-class-notifications-btn">
                                    <i class="fas fa-check"></i> Mark All as Read
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" id="minimize-class-notifications-btn" title="Minimize section">
                                    <i class="fas fa-chevron-up"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Class Code Notifications Container -->
                        <div id="class-code-notifications-container">
                            <!-- Class code notifications will be displayed here -->
                        </div>
                    </div>

                    <div id="student-classroom-container" class="row"></div>







                    <style>
                        #add-classroom-btn {
                            margin-bottom: 20px;
                            /* Adjust as needed */
                        }

                        /* Optional: For a simple overlay background */
                        #overlay {
                            display: none;
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background-color: rgba(0, 0, 0, 0.5);
                            z-index: 1;
                        }

                        /* Ensures the join-classroom card is hidden by default */
                        .hidden {
                            display: none;
                        }

                        /* Ensure proper grid layout for classroom cards */
                        #student-classroom-container.row {
                            display: flex;
                            flex-wrap: wrap;
                            margin-right: -15px;
                            margin-left: -15px;
                        }

                        #student-classroom-container .col-xl-3,
                        #student-classroom-container .col-md-6 {
                            padding-right: 15px;
                            padding-left: 15px;
                            margin-bottom: 1.5rem;
                        }

                        /* Classroom materials section styling */
                        #classroom-materials-section {
                            animation: fadeIn 0.3s ease-in;
                        }

                        @keyframes fadeIn {
                            from { opacity: 0; }
                            to { opacity: 1; }
                        }

                        #back-to-classrooms {
                            transition: all 0.2s ease;
                        }

                        #back-to-classrooms:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        }
                    </style>



                        <!-- Background Overlay -->
                        <div id="overlay" class="hidden"></div>

                        <div id="classroom-container" class="row"></div>
                    </div> <!-- End of main-classroom-section -->

                    <script src="https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js"></script>
                    <script src="https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js"></script>
                    <script src="https://www.gstatic.com/firebasejs/10.11.1/firebase-auth.js"></script>

                    <script type="module">
                        // Global navigation flag
                        window.isNavigatingWithinLMS = false;

                        // Import Firebase functions
                        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js";
                        import { getFirestore, collection, addDoc, getDocs, query, where, deleteDoc, getDoc, doc } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

                        // Firebase configuration
                        const firebaseConfig = {
                            apiKey: "AIzaSyBuj8sMvbDKmjkAVG5JdVOdEF4OO9ijjzA",
                            authDomain: "lead-login.firebaseapp.com",
                            projectId: "lead-login",
                            storageBucket: "lead-login.appspot.com",
                            messagingSenderId: "1051456252675",
                            appId: "1:1051456252675:web:61073e11903055f889d736"
                        };

                        // Initialize Firebase
                        const app = initializeApp(firebaseConfig);
                        const db = getFirestore(app);

                        // Function to show alerts - make it globally accessible
                        window.showAlert = function(message, type = 'success') {
                            const alertDiv = document.createElement('div');
                            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                            alertDiv.role = 'alert';
                            alertDiv.innerHTML = `
                                ${message}
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            `;

                            // Add alert to the page
                            const container = document.querySelector('.container-fluid');
                            container.insertBefore(alertDiv, container.firstChild);

                            // Remove alert after 5 seconds
                            setTimeout(() => {
                                alertDiv.remove();
                            }, 5000);
                        }

                        const joinClassroomBtn = document.getElementById('join-classroom-btn');
                        const joinClassroomCard = document.getElementById('join-classroom-card');
                        const overlay = document.getElementById('overlay');
                        const cancelClassroomBtn = document.getElementById('cancel-classroom');

                        // Show the join-classroom card when 'Join' button is clicked
                        joinClassroomBtn.addEventListener('click', function () {
                            joinClassroomCard.classList.remove('hidden');
                            overlay.classList.remove('hidden'); // Show overlay
                        });

                        // Close the card when 'Cancel' button is clicked
                        cancelClassroomBtn.addEventListener('click', function () {
                            joinClassroomCard.classList.add('hidden');
                            overlay.classList.add('hidden'); // Hide overlay
                        });

                        // Close the card when the overlay is clicked (clicking outside the card)
                        overlay.addEventListener('click', function () {
                            joinClassroomCard.classList.add('hidden');
                            overlay.classList.add('hidden'); // Hide overlay
                        });

                        // Display student card for enrolled classroom
                        function displayStudentCard(classroom) {
                            const container = document.getElementById('student-classroom-container');
                            const card = document.createElement('div');
                            card.className = 'col-xl-3 col-md-6 mb-4';
                            card.innerHTML = `
                                <div class="card border-left-primary shadow h-100 py-2 clickable-card" style="border-left-color: ${classroom.color || '#4e73df'} !important;">
                                    <div class="dropdown">
                                        <button class="btn btn-link" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuButton">
                                            <a class="dropdown-item unenroll-btn" href="#" data-enroll-code="${classroom.enrollCode}">
                                                <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                                Unenroll
                                            </a>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1 classroom-card-text truncate-single subject-text ${(classroom.subjectName && classroom.subjectName.length > 25) ? 'text-tooltip' : ''}" ${(classroom.subjectName && classroom.subjectName.length > 25) ? `data-full-text="${classroom.subjectName}" title="${classroom.subjectName}"` : ''}>
                                                    ${classroom.subjectName || 'No Subject Name'}
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800 classroom-card-text truncate details-text ${(classroom.details && classroom.details.length > 30) ? 'text-tooltip' : ''}" ${(classroom.details && classroom.details.length > 30) ? `data-full-text="${classroom.details}" title="${classroom.details}"` : ''}>${classroom.details || 'No Details'}</div>

                                                <div class="classroom-info mt-3">
                                                    <div class="info-item">
                                                        <i class="fas fa-graduation-cap text-primary"></i>
                                                        <span>Grade: ${classroom.gradeLevel || 'Not Specified'}</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <i class="fas fa-users text-primary"></i>
                                                        <span>Section: ${classroom.sectionName || 'Not Specified'}</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <i class="fas fa-book text-primary"></i>
                                                        <span>Strand: ${classroom.course || 'Not Specified'}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fas fa-book fa-2x text-gray-300"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                            container.appendChild(card);

                            // Add click event to view classroom materials
                            card.addEventListener('click', function(e) {
                                console.log('Card clicked, target:', e.target);
                                console.log('Closest dropdown:', e.target.closest('.dropdown-menu'));
                                console.log('Closest unenroll:', e.target.closest('.unenroll-btn'));
                                console.log('Closest btn-link:', e.target.closest('.btn-link'));

                                // Only show materials if clicking outside the dropdown menu and unenroll button
                                if (!e.target.closest('.dropdown-menu') && !e.target.closest('.unenroll-btn') && !e.target.closest('.btn-link')) {
                                    console.log('Classroom card clicked, showing materials for:', classroom.id);
                                    showClassroomMaterials(classroom);
                                }
                            });

                            // Add event listener for unenroll button
                            const unenrollBtn = card.querySelector('.unenroll-btn');
                            unenrollBtn.addEventListener('click', function(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                const enrollCode = this.getAttribute('data-enroll-code');
                                if (confirm('Are you sure you want to unenroll from this classroom?')) {
                                    unenrollStudent(enrollCode, card);
                                }
                            });
                        }

                        // Function to show classroom materials within the same page
                        function showClassroomMaterials(classroom) {
                            // Hide main classroom section
                            document.getElementById('main-classroom-section').style.display = 'none';
                            document.getElementById('main-heading').style.display = 'none';

                            // Show classroom materials section
                            document.getElementById('classroom-materials-section').style.display = 'block';

                            // Update classroom information
                            document.getElementById('classroom-title').textContent = `${classroom.subjectName || 'Materials'}`;
                            document.getElementById('subject-name').textContent = classroom.subjectName || 'Not specified';
                            document.getElementById('classroom-details').textContent = classroom.details || 'Not specified';
                            document.getElementById('created-by').textContent = classroom.createdBy || 'Not specified';
                            document.getElementById('grade-level').textContent = classroom.gradeLevel || 'Not specified';
                            document.getElementById('section-name').textContent = classroom.sectionName || 'Not specified';
                            document.getElementById('course-strand').textContent = classroom.course || 'Not specified';

                            // Set header color to match classroom color
                            const header = document.getElementById('classroom-header');
                            header.style.backgroundColor = classroom.color || '#4e73df';

                            // Load materials for this classroom
                            loadClassroomMaterials(classroom.id);
                        }

                        // Function to go back to main classroom list
                        function showMainClassroomList() {
                            // Hide classroom materials section
                            document.getElementById('classroom-materials-section').style.display = 'none';

                            // Show main classroom section
                            document.getElementById('main-classroom-section').style.display = 'block';
                            document.getElementById('main-heading').style.display = 'block';
                        }

                        // Function to load classroom materials
                        async function loadClassroomMaterials(classroomId) {
                            const materialsContainer = document.getElementById('materials-container');
                            materialsContainer.innerHTML = '<p class="text-muted">Loading materials...</p>';

                            try {
                                // Query materials for this classroom
                                const materialsRef = collection(db, "Materials");
                                const q = query(materialsRef, where("classroomId", "==", classroomId));
                                const querySnapshot = await getDocs(q);

                                if (querySnapshot.empty) {
                                    materialsContainer.innerHTML = '<p class="text-muted">No materials available for this classroom yet.</p>';
                                    return;
                                }

                                let materialsHtml = '';
                                querySnapshot.forEach((doc) => {
                                    const material = doc.data();
                                    const uploadDate = material.uploadDate?.toDate?.() || new Date(material.uploadDate);

                                    materialsHtml += `
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <h6 class="card-title">${material.title || 'Untitled Material'}</h6>
                                                <p class="card-text text-muted">${material.description || 'No description available'}</p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">Uploaded: ${uploadDate.toLocaleDateString()}</small>
                                                    ${material.fileUrl ? `<a href="${material.fileUrl}" target="_blank" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-download"></i> Download
                                                    </a>` : ''}
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                });

                                materialsContainer.innerHTML = materialsHtml;

                            } catch (error) {
                                console.error("Error loading materials:", error);
                                materialsContainer.innerHTML = '<p class="text-danger">Error loading materials. Please try again.</p>';
                            }
                        }

                        // Function to enroll student in a classroom
                        async function enrollStudent(classroomData) {
                            try {
                                // Get student data from localStorage
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    alert("Please log in first.");
                                    return;
                                }

                                // Check if already enrolled by querying Firestore
                                const enrollmentsRef = collection(db, "Enrollments");
                                const q = query(
                                    enrollmentsRef,
                                    where("studentId", "==", studentData.email),
                                    where("classroomId", "==", classroomData.id)
                                );
                                const querySnapshot = await getDocs(q);

                                if (!querySnapshot.empty) {
                                    showAlert('You are already enrolled in this classroom', 'danger');
                                    return;
                                }

                                // Add enrollment to Firestore
                                const enrollmentData = {
                                    studentId: studentData.email,
                                    studentName: `${studentData.firstName} ${studentData.lastName}`,
                                    studentEmail: studentData.email,
                                    classroomId: classroomData.id,
                                    classroomName: classroomData.subjectName,
                                    enrollCode: classroomData.enrollCode,
                                    gradeLevel: classroomData.gradeLevel,
                                    enrolledAt: new Date().toISOString()
                                };

                                // Add to Enrollments collection in Firestore
                                await addDoc(collection(db, "Enrollments"), enrollmentData);

                                // Display the new classroom card
                                displayStudentCard(classroomData);

                                // Show success message
                                showAlert('Successfully enrolled in the classroom', 'success');
                            } catch (error) {
                                console.error("Error enrolling student:", error);
                                showAlert('Failed to enroll in classroom. Please try again.', 'danger');
                            }
                        }

                        // Function to unenroll student from a classroom
                        async function unenrollStudent(enrollCode, cardElement) {
                            try {
                                // Get student data
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    alert("Please log in first.");
                                    return;
                                }

                                // Query Firestore to find the enrollment using email as studentId
                                const enrollmentsRef = collection(db, "Enrollments");
                                const q = query(
                                    enrollmentsRef,
                                    where("studentId", "==", studentData.email),
                                    where("enrollCode", "==", enrollCode)
                                );
                                const querySnapshot = await getDocs(q);

                                // Delete enrollment from Firestore
                                querySnapshot.forEach(async (doc) => {
                                    await deleteDoc(doc.ref);
                                });

                                // Remove the card from the UI
                                cardElement.remove();

                                // Show success message
                                showAlert('Successfully unenrolled from the classroom', 'success');
                            } catch (error) {
                                console.error("Error unenrolling student:", error);
                                showAlert('Failed to unenroll from classroom. Please try again.', 'danger');
                            }
                        }

                        // Function to load enrolled classrooms
                        async function loadEnrolledClassrooms() {
                            try {
                                // Get student data
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    console.log("No student data found, skipping classroom load");
                                    return;
                                }

                                console.log("Loading enrolled classrooms for student:", studentData.email);

                                // Query Firestore for student's enrollments using email as studentId
                                const enrollmentsRef = collection(db, "Enrollments");
                                const q = query(
                                    enrollmentsRef,
                                    where("studentId", "==", studentData.email)
                                );

                                const querySnapshot = await getDocs(q);
                                console.log("Found enrollments:", querySnapshot.size);

                                if (querySnapshot.empty) {
                                    console.log("No enrollments found for student");
                                    return;
                                }

                                // Get all classroom IDs from enrollments
                                const classroomIds = [];
                                querySnapshot.forEach((doc) => {
                                    const enrollment = doc.data();
                                    console.log("Processing enrollment:", enrollment);
                                    classroomIds.push(enrollment.classroomId);
                                });

                                // Get classroom details for each enrollment
                                const classrooms = [];
                                for (const classroomId of classroomIds) {
                                    try {
                                        const classroomDoc = await getDoc(doc(db, "Classrooms", classroomId));
                                        if (classroomDoc.exists()) {
                                            const classroomData = classroomDoc.data();
                                            classroomData.id = classroomId;
                                            classrooms.push(classroomData);
                                            console.log("Loaded classroom:", classroomData.subjectName);
                                        } else {
                                            console.warn("Classroom not found:", classroomId);
                                        }
                                    } catch (classroomError) {
                                        console.error("Error loading classroom:", classroomId, classroomError);
                                    }
                                }

                                if (classrooms.length === 0) {
                                    console.log("No valid classrooms found");
                                    return;
                                }

                                // Display classrooms
                                const container = document.getElementById('student-classroom-container');
                                if (container) {
                                    container.innerHTML = ''; // Clear existing content
                                    classrooms.forEach(classroom => {
                                        displayStudentCard(classroom);
                                    });
                                } else {
                                    console.error("Student classroom container not found");
                                }
                            } catch (error) {
                                console.error("Error in loadEnrolledClassrooms:", error);
                                if (error.message !== "Failed to load enrolled classrooms. Please try again.") {
                                    console.error("Error details:", {
                                        message: error.message,
                                        code: error.code,
                                        stack: error.stack
                                    });
                                }
                            }
                        }

                        // Function to load quiz notifications only for enrolled classrooms
                        async function loadQuizNotifications() {
                            try {
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    return;
                                }

                                // Get student's enrolled classrooms first
                                const enrollmentsRef = collection(db, "Enrollments");
                                const enrollmentQuery = query(enrollmentsRef, where("studentId", "==", studentData.email));
                                const enrollmentSnapshot = await getDocs(enrollmentQuery);

                                if (enrollmentSnapshot.empty) {
                                    return; // No enrolled classrooms
                                }

                                // Get classroom IDs
                                const classroomIds = [];
                                enrollmentSnapshot.forEach((doc) => {
                                    classroomIds.push(doc.data().classroomId);
                                });

                                // Get only quiz notifications
                                const quizNotifications = await getQuizNotifications(classroomIds);

                                if (quizNotifications.length === 0) {
                                    document.getElementById('quiz-notifications-section').style.display = 'none';
                                    return;
                                }

                                // Sort by sent date (newest first)
                                quizNotifications.sort((a, b) => {
                                    const aTime = a.sentAt?.toDate?.() || new Date(a.sentAt);
                                    const bTime = b.sentAt?.toDate?.() || new Date(b.sentAt);
                                    return bTime.getTime() - aTime.getTime();
                                });

                                displayQuizNotifications(quizNotifications);

                                // Update notification badge (only quiz notifications)
                                const unreadCount = quizNotifications.filter(n => !n.isRead).length;
                                updateNotificationBadge(unreadCount);

                            } catch (error) {
                                console.error("Error loading quiz notifications:", error);
                            }
                        }

                        // Function to get quiz notifications
                        async function getQuizNotifications(classroomIds) {
                            const notificationsRef = collection(db, "QuizNotifications");
                            const notificationQuery = query(
                                notificationsRef,
                                where("classroomId", "in", classroomIds)
                            );
                            const notificationSnapshot = await getDocs(notificationQuery);

                            const notifications = [];
                            notificationSnapshot.forEach((doc) => {
                                notifications.push({ id: doc.id, ...doc.data() });
                            });
                            return notifications;
                        }

                        // Function to get class code notifications
                        async function getClassCodeNotifications(studentEmail) {
                            const notificationsRef = collection(db, "ClassCodeNotifications");
                            const notificationQuery = query(
                                notificationsRef,
                                where("studentEmail", "==", studentEmail)
                            );
                            const notificationSnapshot = await getDocs(notificationQuery);

                            const notifications = [];
                            notificationSnapshot.forEach((doc) => {
                                notifications.push({ id: doc.id, ...doc.data() });
                            });
                            return notifications;
                        }

                        // Function to load and display class code notifications in My Class section
                        async function loadClassCodeNotifications() {
                            try {
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    return;
                                }

                                // Get class code notifications for this student
                                const classCodeNotifications = await getClassCodeNotifications(studentData.email);

                                if (classCodeNotifications.length === 0) {
                                    document.getElementById('class-code-notifications-section').style.display = 'none';
                                    return;
                                }

                                // Sort by sent date (newest first)
                                classCodeNotifications.sort((a, b) => {
                                    const aTime = a.sentAt?.toDate?.() || new Date(a.sentAt);
                                    const bTime = b.sentAt?.toDate?.() || new Date(b.sentAt);
                                    return bTime.getTime() - aTime.getTime();
                                });

                                displayClassCodeNotifications(classCodeNotifications);

                                // Show the section
                                document.getElementById('class-code-notifications-section').style.display = 'block';

                            } catch (error) {
                                console.error("Error loading class code notifications:", error);
                            }
                        }

                        // Function to display class code notifications in My Class section
                        function displayClassCodeNotifications(notifications) {
                            const container = document.getElementById('class-code-notifications-container');

                            let html = '';
                            notifications.forEach(notification => {
                                const sentDate = notification.sentAt?.toDate?.() || new Date(notification.sentAt);
                                const timeAgo = getTimeAgo(sentDate);

                                // Build classroom context information
                                const classroomInfo = [];
                                if (notification.gradeLevel) {
                                    classroomInfo.push(`<strong>Grade:</strong> ${notification.gradeLevel}`);
                                }
                                if (notification.sectionName) {
                                    classroomInfo.push(`<strong>Section:</strong> ${notification.sectionName}`);
                                }
                                if (notification.course) {
                                    classroomInfo.push(`<strong>Strand:</strong> ${notification.course}`);
                                }

                                const classroomInfoHtml = classroomInfo.length > 0 ?
                                    `<div class="mb-2 p-2 bg-light rounded">
                                        <small class="text-muted d-block mb-1"><i class="fas fa-info-circle"></i> Class Details:</small>
                                        <small class="text-muted">${classroomInfo.join(' • ')}</small>
                                    </div>` : '';

                                html += `
                                    <div class="card mb-3 notification-card ${notification.isRead ? 'read' : 'unread'}" data-notification-id="${notification.id}" data-notification-type="classCode">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <span class="badge badge-success mr-2">${notification.label}</span>
                                                        ${!notification.isRead ? '<span class="badge badge-warning">New</span>' : ''}
                                                    </div>
                                                    <h6 class="card-title mb-2 text-dark">
                                                        <i class="fas fa-key text-success"></i> ${notification.classroomName}
                                                    </h6>
                                                    <p class="card-text text-muted mb-2">
                                                        <strong>Enrollment Code:</strong>
                                                        <span class="font-weight-bold text-success">${notification.enrollCode}</span>
                                                        <button class="btn btn-sm btn-link ml-1 p-0 copy-class-code-btn" data-class-code="${notification.enrollCode}" title="Copy class code">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </p>
                                                    ${classroomInfoHtml}
                                                    ${notification.message ? `<p class="card-text">${notification.message}</p>` : ''}
                                                    <small class="text-muted">
                                                        From: ${notification.sentByName} • ${timeAgo}
                                                    </small>
                                                </div>
                                                <div class="ml-3 d-flex flex-column">
                                                    <button class="btn btn-success btn-sm join-class-btn mb-2" data-class-code="${notification.enrollCode}">
                                                        <i class="fas fa-plus"></i> Join Class
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm remove-class-notification-btn" data-notification-id="${notification.id}" title="Remove notification">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });

                            container.innerHTML = html;

                            // Add event listeners for the new buttons
                            addClassCodeNotificationEventListeners();

                            // Restore minimize state
                            restoreClassCodeNotificationsMinimizeState();
                        }

                        // Function to add event listeners for class code notifications
                        function addClassCodeNotificationEventListeners() {
                            // Add event listeners for copy class code buttons
                            document.querySelectorAll('.copy-class-code-btn').forEach(button => {
                                button.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                    const classCode = this.getAttribute('data-class-code');
                                    navigator.clipboard.writeText(classCode).then(() => {
                                        const originalIcon = this.innerHTML;
                                        this.innerHTML = '<i class="fas fa-check text-success"></i>';
                                        this.title = 'Copied!';
                                        setTimeout(() => {
                                            this.innerHTML = originalIcon;
                                            this.title = 'Copy class code';
                                        }, 2000);
                                    }).catch(err => {
                                        console.error('Failed to copy class code: ', err);
                                        alert('Failed to copy class code. Please try again.');
                                    });
                                });
                            });

                            // Add event listeners for join class buttons
                            document.querySelectorAll('.join-class-btn').forEach(button => {
                                button.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                    const classCode = this.getAttribute('data-class-code');

                                    if (confirm(`Do you want to join the class with code: ${classCode}?`)) {
                                        // Use the existing enrollment functionality
                                        const codeInput = document.getElementById('code');
                                        if (codeInput) {
                                            codeInput.value = classCode;
                                            // Trigger the join classroom process
                                            const submitButton = document.getElementById('submit-code');
                                            if (submitButton) {
                                                submitButton.click();
                                            }
                                        }
                                    }
                                });
                            });

                            // Add event listeners for remove class notification buttons
                            document.querySelectorAll('.remove-class-notification-btn').forEach(button => {
                                button.addEventListener('click', function() {
                                    const notificationId = this.getAttribute('data-notification-id');
                                    removeNotification(notificationId, 'classCode');
                                });
                            });
                        }

                        // Function to update notification badge
                        function updateNotificationBadge(count) {
                            const badge = document.getElementById('notification-badge');
                            if (count > 0) {
                                badge.textContent = count;
                                badge.style.display = 'inline';
                            } else {
                                badge.style.display = 'none';
                            }
                        }

                        // Function to group quiz notifications
                        function groupQuizNotifications(notifications, groupOption) {
                            if (groupOption === 'none') {
                                return { 'All Notifications': notifications };
                            }

                            const groups = {};

                            notifications.forEach(notification => {
                                let groupKey;

                                switch (groupOption) {
                                    case 'subject':
                                        groupKey = notification.subjectName || 'Unknown Subject';
                                        break;
                                    case 'grade':
                                        groupKey = notification.gradeLevel || 'Unknown Grade';
                                        break;
                                    case 'section':
                                        groupKey = notification.sectionName || 'Unknown Section';
                                        break;
                                    case 'course':
                                        groupKey = notification.course || 'Unknown Strand';
                                        break;
                                    case 'status':
                                        groupKey = notification.isRead ? 'Read' : 'Unread';
                                        break;
                                    default:
                                        groupKey = 'All Notifications';
                                }

                                if (!groups[groupKey]) {
                                    groups[groupKey] = [];
                                }
                                groups[groupKey].push(notification);
                            });

                            return groups;
                        }

                        // Function to sort quiz notifications
                        function sortQuizNotifications(a, b, sortOption) {
                            switch (sortOption) {
                                case 'name-asc':
                                    return (a.quizTitle || 'Untitled').toLowerCase().localeCompare((b.quizTitle || 'Untitled').toLowerCase());
                                case 'name-desc':
                                    return (b.quizTitle || 'Untitled').toLowerCase().localeCompare((a.quizTitle || 'Untitled').toLowerCase());
                                case 'date-newest':
                                    const aTime = a.sentAt?.toDate?.() || new Date(a.sentAt || 0);
                                    const bTime = b.sentAt?.toDate?.() || new Date(b.sentAt || 0);
                                    return bTime.getTime() - aTime.getTime();
                                case 'date-oldest':
                                    const aTime2 = a.sentAt?.toDate?.() || new Date(a.sentAt || 0);
                                    const bTime2 = b.sentAt?.toDate?.() || new Date(b.sentAt || 0);
                                    return aTime2.getTime() - bTime2.getTime();
                                case 'subject-asc':
                                    return (a.subjectName || 'Unknown').toLowerCase().localeCompare((b.subjectName || 'Unknown').toLowerCase());
                                case 'subject-desc':
                                    return (b.subjectName || 'Unknown').toLowerCase().localeCompare((a.subjectName || 'Unknown').toLowerCase());
                                default:
                                    return 0;
                            }
                        }

                        // Function to create group header for notifications
                        function createNotificationGroupHeader(groupName, notificationCount, isMinimized = false) {
                            const groupId = `notification-group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;

                            return `
                                <div class="group-header" data-group="${groupId}">
                                    <div class="group-info">
                                        ${groupName}
                                        <span class="group-count">${notificationCount} notification${notificationCount !== 1 ? 's' : ''}</span>
                                    </div>
                                    <button class="group-minimize-btn" type="button" title="${isMinimized ? 'Expand' : 'Minimize'} group">
                                        <i class="fas ${isMinimized ? 'fa-chevron-down' : 'fa-chevron-up'}"></i>
                                    </button>
                                </div>
                                <div class="group-content" id="${groupId}" style="${isMinimized ? 'display: none;' : ''}">
                            `;
                        }

                        // Function to display quiz notifications
                        function displayQuizNotifications(notifications) {
                            const container = document.getElementById('quiz-notifications-container');
                            const section = document.getElementById('quiz-notifications-section');

                            if (notifications.length === 0) {
                                section.style.display = 'none';
                                return;
                            }

                            section.style.display = 'block';

                            // Get grouping and sorting preferences
                            const groupOption = document.getElementById('quiz-notifications-group-select')?.value ||
                                               localStorage.getItem('quizNotificationsGroupPreference') || 'none';
                            const sortOption = document.getElementById('quiz-notifications-sort-select')?.value ||
                                              localStorage.getItem('quizNotificationsSortPreference') || 'date-newest';

                            // Sort notifications first
                            notifications.sort((a, b) => sortQuizNotifications(a, b, sortOption));

                            // Group notifications
                            const groupedNotifications = groupQuizNotifications(notifications, groupOption);

                            let html = '';
                            Object.keys(groupedNotifications).forEach(groupName => {
                                const notificationsInGroup = groupedNotifications[groupName];

                                if (groupOption !== 'none') {
                                    const groupId = `notification-group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                                    const isMinimized = localStorage.getItem(`${groupId}-minimized`) === 'true';
                                    html += createNotificationGroupHeader(groupName, notificationsInGroup.length, isMinimized);
                                }

                                notificationsInGroup.forEach(notification => {
                                    const sentDate = notification.sentAt?.toDate?.() || new Date(notification.sentAt);
                                    const updatedDate = notification.updatedAt?.toDate?.() || null;
                                    const timeAgo = getTimeAgo(sentDate);
                                    const isUpdated = updatedDate && updatedDate.getTime() !== sentDate.getTime();

                                    // Build classroom context information
                                    const classroomInfo = [];
                                    if (notification.subjectName || notification.classroomName) {
                                        classroomInfo.push(`<strong>Subject:</strong> ${notification.subjectName || notification.classroomName}`);
                                    }
                                    if (notification.gradeLevel) {
                                        classroomInfo.push(`<strong>Grade:</strong> ${notification.gradeLevel}`);
                                    }
                                    if (notification.sectionName) {
                                        classroomInfo.push(`<strong>Section:</strong> ${notification.sectionName}`);
                                    }
                                    if (notification.course) {
                                        classroomInfo.push(`<strong>Strand:</strong> ${notification.course}`);
                                    }

                                    const classroomInfoHtml = classroomInfo.length > 0 ?
                                        `<div class="mb-2 p-2 bg-light rounded">
                                            <small class="text-muted d-block mb-1"><i class="fas fa-classroom"></i> Classroom Details:</small>
                                            <small class="text-muted">${classroomInfo.join(' • ')}</small>
                                        </div>` : '';

                                    // Quiz Notification (only quiz notifications in this section now)
                                    html += `
                                        <div class="card mb-3 notification-card ${notification.isRead ? 'read' : 'unread'}" data-notification-id="${notification.id}" data-notification-type="quiz">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <div class="d-flex align-items-center mb-2">
                                                            <span class="badge badge-primary mr-2">${notification.label}</span>
                                                            ${!notification.isRead ? '<span class="badge badge-warning">New</span>' : ''}
                                                            ${isUpdated ? '<span class="badge badge-info ml-1">Updated</span>' : ''}
                                                        </div>
                                                        <h6 class="card-title mb-2 text-dark">
                                                            <i class="fas fa-quiz text-primary"></i> ${notification.quizTitle}
                                                        </h6>
                                                        <p class="card-text text-muted mb-2">
                                                            <strong>Quiz Code:</strong>
                                                            <span class="font-weight-bold text-primary">${notification.quizCode}</span>
                                                            <button class="btn btn-sm btn-link ml-1 p-0 copy-quiz-code-btn" data-quiz-code="${notification.quizCode}" title="Copy quiz code">
                                                                <i class="fas fa-copy"></i>
                                                            </button>
                                                        </p>
                                                        ${classroomInfoHtml}
                                                        ${notification.message ? `<p class="card-text">${notification.message}</p>` : ''}
                                                        <small class="text-muted">
                                                            From: ${notification.sentByName} • ${timeAgo}
                                                            ${isUpdated ? ` • Updated: ${getTimeAgo(updatedDate)}` : ''}
                                                        </small>
                                                    </div>
                                                    <div class="ml-3 d-flex flex-column">
                                                        <button class="btn btn-primary btn-sm take-quiz-btn mb-2" data-quiz-code="${notification.quizCode}">
                                                            <i class="fas fa-play"></i> Take Quiz
                                                        </button>
                                                        <button class="btn btn-outline-danger btn-sm remove-notification-btn" data-notification-id="${notification.id}" data-notification-type="quiz" title="Remove notification">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                });

                                if (groupOption !== 'none') {
                                    html += '</div>'; // Close group-content div
                                }
                            });

                            container.innerHTML = html;

                            // Add event listeners for take quiz buttons
                            document.querySelectorAll('.take-quiz-btn').forEach(button => {
                                button.addEventListener('click', function() {
                                    const quizCode = this.getAttribute('data-quiz-code');
                                    takeQuizByCode(quizCode);
                                });
                            });

                            // Add event listeners for remove notification buttons
                            document.querySelectorAll('.remove-notification-btn').forEach(button => {
                                button.addEventListener('click', function() {
                                    const notificationId = this.getAttribute('data-notification-id');
                                    const notificationType = this.getAttribute('data-notification-type') || 'quiz';
                                    removeNotification(notificationId, notificationType);
                                });
                            });

                            // Add event listeners for copy quiz code buttons
                            document.querySelectorAll('.copy-quiz-code-btn').forEach(button => {
                                button.addEventListener('click', function(e) {
                                    e.stopPropagation();
                                    const quizCode = this.getAttribute('data-quiz-code');
                                    navigator.clipboard.writeText(quizCode).then(() => {
                                        const originalIcon = this.innerHTML;
                                        this.innerHTML = '<i class="fas fa-check text-success"></i>';
                                        this.title = 'Copied!';
                                        setTimeout(() => {
                                            this.innerHTML = originalIcon;
                                            this.title = 'Copy quiz code';
                                        }, 2000);
                                    }).catch(err => {
                                        console.error('Failed to copy quiz code: ', err);
                                        alert('Failed to copy quiz code. Please try again.');
                                    });
                                });
                            });



                            // Add event listeners for minimize buttons
                            document.querySelectorAll('.group-minimize-btn').forEach(button => {
                                button.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    e.stopPropagation();

                                    const header = this.closest('.group-header');
                                    if (!header) return;

                                    const groupId = header.getAttribute('data-group');
                                    const groupContent = document.getElementById(groupId);
                                    const icon = this.querySelector('i');

                                    if (groupContent.style.display === 'none') {
                                        groupContent.style.display = 'block';
                                        icon.className = 'fas fa-chevron-up';
                                        this.title = 'Minimize group';
                                        localStorage.removeItem(`${groupId}-minimized`);
                                    } else {
                                        groupContent.style.display = 'none';
                                        icon.className = 'fas fa-chevron-down';
                                        this.title = 'Expand group';
                                        localStorage.setItem(`${groupId}-minimized`, 'true');
                                    }
                                });
                            });
                        }

                        // Function to get time ago string
                        function getTimeAgo(date) {
                            const now = new Date();
                            const diffInSeconds = Math.floor((now - date) / 1000);

                            if (diffInSeconds < 60) return 'Just now';
                            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
                            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
                            return `${Math.floor(diffInSeconds / 86400)} days ago`;
                        }

                        // Function to take quiz by code
                        async function takeQuizByCode(quizCode) {
                            try {
                                // Find quiz by code
                                const quizzesRef = collection(db, "Quizzes");
                                const quizQuery = query(quizzesRef, where("code", "==", quizCode));
                                const quizSnapshot = await getDocs(quizQuery);

                                if (quizSnapshot.empty) {
                                    alert('Quiz not found');
                                    return;
                                }

                                const quizDoc = quizSnapshot.docs[0];
                                const quizId = quizDoc.id;

                                // Redirect to take quiz page
                                window.location.href = `take-quiz.html?id=${quizId}`;

                            } catch (error) {
                                console.error("Error taking quiz:", error);
                                alert('Error accessing quiz. Please try again.');
                            }
                        }

                        // Handle join classroom form submission
                        document.getElementById("submit-code").addEventListener("click", async function() {
                            const codeInput = document.getElementById("code");
                            const enteredCode = codeInput.value.trim();

                            if (!enteredCode) {
                                alert("Please enter an enrollment code.");
                                return;
                            }

                            try {
                                console.log("Searching for classroom with code:", enteredCode);

                                // Query Firestore to find the classroom with matching enroll code
                                const querySnapshot = await getDocs(collection(db, "Classrooms"));
                                let classroomFound = null;

                                querySnapshot.forEach((doc) => {
                                    const classroomData = doc.data();
                                    console.log("Checking classroom:", classroomData);
                                    if (classroomData.enrollCode === enteredCode) {
                                        console.log("Found matching classroom:", classroomData);
                                        classroomFound = {
                                            ...classroomData,
                                            id: doc.id,
                                            enrolledAt: new Date().toISOString()
                                        };
                                    }
                                });

                                if (!classroomFound) {
                                    console.log("No classroom found with code:", enteredCode);
                                    showAlert("Invalid enrollment code. Please check and try again.", "danger");
                                    return;
                                }

                                // Get student data
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    console.log("No student data found in localStorage");
                                    showAlert("Please log in first.", "danger");
                                    return;
                                }

                                // Check if student is already enrolled by querying Firestore
                                const enrollmentsRef = collection(db, "Enrollments");
                                const q = query(
                                    enrollmentsRef,
                                    where("studentId", "==", studentData.email),
                                    where("classroomId", "==", classroomFound.id)
                                );
                                const enrollmentSnapshot = await getDocs(q);

                                if (!enrollmentSnapshot.empty) {
                                    console.log("Student already enrolled in this classroom");
                                    showAlert("You are already enrolled in this classroom.", "warning");
                                    return;
                                }

                                // Add enrollment to Firestore
                                const enrollmentData = {
                                    studentId: studentData.email,
                                    studentName: `${studentData.firstName} ${studentData.lastName}`,
                                    studentEmail: studentData.email,
                                    classroomId: classroomFound.id,
                                    classroomName: classroomFound.subjectName,
                                    enrollCode: classroomFound.enrollCode,
                                    gradeLevel: classroomFound.gradeLevel,
                                    enrolledAt: new Date().toISOString()
                                };

                                console.log("Attempting to create enrollment:", enrollmentData);

                                // Add to Enrollments collection in Firestore
                                await addDoc(collection(db, "Enrollments"), enrollmentData);
                                console.log("Enrollment created successfully");

                                // Display the new classroom card
                                displayStudentCard(classroomFound);

                                // Show success message
                                showAlert(`Successfully enrolled in ${classroomFound.subjectName}`, 'success');

                                // Hide the join form and overlay
                                document.getElementById("join-classroom-card").classList.add("hidden");
                                document.getElementById("overlay").classList.add("hidden");

                                // Clear the input
                                codeInput.value = "";

                                // Show classroom materials after a short delay
                                setTimeout(() => {
                                    console.log('Join classroom, showing materials for:', classroomFound.id);
                                    showClassroomMaterials(classroomFound);
                                }, 1500);
                            } catch (error) {
                                console.error("Error in enrollment process:", error);
                                console.error("Error details:", {
                                    message: error.message,
                                    code: error.code,
                                    stack: error.stack
                                });
                                showAlert('Failed to enroll in classroom. Please try again.', 'danger');
                            }
                        });

                        // Quiz functionality
                        document.addEventListener('DOMContentLoaded', function() {
                            const submitQuizCodeBtn = document.getElementById('submitQuizCode');
                            const quizCodeInput = document.getElementById('quizCode');
                            const quizCodeForm = document.getElementById('quizCodeForm');

                            if (submitQuizCodeBtn) {
                                submitQuizCodeBtn.addEventListener('click', async function() {
                                    const quizCode = quizCodeInput.value.trim();

                                    if (!quizCode) {
                                        showAlert('Please enter a quiz code', 'danger');
                                        return;
                                    }

                                    try {
                                        // Query Firestore for the quiz code
                                        const quizzesRef = collection(db, "Quizzes");
                                        const q = query(quizzesRef, where("code", "==", quizCode));
                                        const querySnapshot = await getDocs(q);

                                        if (querySnapshot.empty) {
                                            showAlert('Invalid quiz code. Please check and try again.', 'danger');
                                            return;
                                        }

                                        // Get the first matching quiz
                                        const quizDoc = querySnapshot.docs[0];
                                        const quizData = quizDoc.data();

                                        // Store the quiz ID and data in localStorage for the quiz page
                                        localStorage.setItem('currentQuiz', JSON.stringify({
                                            id: quizDoc.id,
                                            ...quizData
                                        }));

                                        // Clear the input and close the modal
                                        quizCodeInput.value = '';
                                        $('#takeQuizModal').modal('hide');

                                        // Redirect to the quiz page
                                        window.location.href = `take-quiz.html?id=${quizDoc.id}`;

                                    } catch (error) {
                                        console.error("Error finding quiz:", error);
                                        showAlert('An error occurred while finding the quiz. Please try again.', 'danger');
                                    }
                                });
                            }

                            // Prevent form submission on enter key
                            if (quizCodeForm) {
                                quizCodeForm.addEventListener('submit', function(e) {
                                    e.preventDefault();
                                    submitQuizCodeBtn.click();
                                });
                            }
                        });

                        // Function to load and display quiz scores
                        async function loadQuizScores() {
                            try {
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    console.log("No student data found");
                                    return;
                                }

                                // Query Firestore for student's quiz submissions
                                const submissionsRef = collection(db, "QuizSubmissions");
                                const q = query(
                                    submissionsRef,
                                    where("studentId", "==", studentData.email)
                                );

                                const querySnapshot = await getDocs(q);

                                if (querySnapshot.empty) {
                                    document.getElementById('quiz-scores-container').innerHTML =
                                        '<div class="alert alert-info">You haven\'t taken any quizzes yet.</div>';
                                    return;
                                }

                                // Get all submissions and sort by submission date
                                const submissions = [];
                                querySnapshot.forEach((doc) => {
                                    submissions.push({ id: doc.id, ...doc.data() });
                                });

                                submissions.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));

                                // Group submissions by quiz
                                const quizSubmissions = {};
                                submissions.forEach(submission => {
                                    if (!quizSubmissions[submission.quizId]) {
                                        quizSubmissions[submission.quizId] = {
                                            quizTitle: submission.quizTitle,
                                            quizId: submission.quizId,
                                            attempts: []
                                        };
                                    }
                                    quizSubmissions[submission.quizId].attempts.push(submission);
                                });

                                // Create HTML for quiz submissions grouped by quiz
                                const scoresHTML = Object.values(quizSubmissions).map(quiz => {
                                    // Sort attempts by date (newest first)
                                    const attempts = quiz.attempts.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
                                    const latestAttempt = attempts[0]; // Most recent attempt

                                    return `
                                    <div class="quiz-item mb-4">
                                        <div class="card shadow">
                                            <div class="card-header py-3">
                                                <h6 class="m-0 font-weight-bold text-primary">${quiz.quizTitle || 'Unknown Quiz'}</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <h6 class="font-weight-bold">Latest Attempt</h6>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="badge ${latestAttempt.percentage >= 70 ? 'badge-success' : 'badge-danger'} p-2">
                                                                Score: ${latestAttempt.percentage.toFixed(1)}%
                                                            </span>
                                                            <small class="ml-2 text-muted">
                                                                (${latestAttempt.score} out of ${latestAttempt.maxScore})
                                                            </small>
                                                        </div>
                                                        <small class="text-muted">
                                                            ${new Date(latestAttempt.submittedAt).toLocaleString()}
                                                        </small>
                                                    </div>
                                                </div>

                                                ${attempts.length > 1 ? `
                                                <div class="mt-3">
                                                    <h6 class="font-weight-bold">Previous Attempts</h6>
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>Attempt #</th>
                                                                    <th>Score</th>
                                                                    <th>Percentage</th>
                                                                    <th>Date</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                ${attempts.slice(1).map((attempt, index) => `
                                                                <tr>
                                                                    <td>${attempts.length - index - 1}</td>
                                                                    <td>${attempt.score} / ${attempt.maxScore}</td>
                                                                    <td>
                                                                        <span class="badge ${attempt.percentage >= 70 ? 'badge-success' : 'badge-danger'}">
                                                                            ${attempt.percentage.toFixed(1)}%
                                                                        </span>
                                                                    </td>
                                                                    <td>${new Date(attempt.submittedAt).toLocaleString()}</td>
                                                                </tr>
                                                                `).join('')}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                    `;
                                }).join('');

                                document.getElementById('quiz-scores-container').innerHTML = scoresHTML;

                            } catch (error) {
                                console.error("Error loading quiz scores:", error);
                                document.getElementById('quiz-scores-container').innerHTML =
                                    '<div class="alert alert-danger">Error loading quiz scores. Please try again later.</div>';
                            }
                        }

                        // Function to mark all notifications as read
                        async function markAllNotificationsAsRead() {
                            try {
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    return;
                                }

                                // Get student's enrolled classrooms first
                                const enrollmentsRef = collection(db, "Enrollments");
                                const enrollmentQuery = query(enrollmentsRef, where("studentId", "==", studentData.email));
                                const enrollmentSnapshot = await getDocs(enrollmentQuery);

                                if (enrollmentSnapshot.empty) {
                                    return;
                                }

                                // Get classroom IDs
                                const classroomIds = [];
                                enrollmentSnapshot.forEach((doc) => {
                                    classroomIds.push(doc.data().classroomId);
                                });

                                // Get unread quiz notifications for these classrooms
                                const quizNotificationsRef = collection(db, "QuizNotifications");
                                const quizNotificationQuery = query(
                                    quizNotificationsRef,
                                    where("classroomId", "in", classroomIds),
                                    where("isRead", "==", false)
                                );

                                // Get unread class code notifications for this student
                                const classCodeNotificationsRef = collection(db, "ClassCodeNotifications");
                                const classCodeNotificationQuery = query(
                                    classCodeNotificationsRef,
                                    where("studentEmail", "==", studentData.email),
                                    where("isRead", "==", false)
                                );

                                // Fetch both types of notifications
                                const [quizNotificationSnapshot, classCodeNotificationSnapshot] = await Promise.all([
                                    getDocs(quizNotificationQuery),
                                    getDocs(classCodeNotificationQuery)
                                ]);

                                // Update all notifications to read
                                const { updateDoc, doc } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");
                                const updatePromises = [];

                                // Update quiz notifications
                                quizNotificationSnapshot.forEach((notificationDoc) => {
                                    const notificationRef = doc(db, "QuizNotifications", notificationDoc.id);
                                    updatePromises.push(updateDoc(notificationRef, { isRead: true }));
                                });

                                // Update class code notifications
                                classCodeNotificationSnapshot.forEach((notificationDoc) => {
                                    const notificationRef = doc(db, "ClassCodeNotifications", notificationDoc.id);
                                    updatePromises.push(updateDoc(notificationRef, { isRead: true }));
                                });

                                if (updatePromises.length > 0) {
                                    await Promise.all(updatePromises);
                                }

                                // Reload notifications to update the display
                                loadQuizNotifications();

                            } catch (error) {
                                console.error("Error marking notifications as read:", error);
                            }
                        }

                        // Function to remove a single notification
                        async function removeNotification(notificationId, notificationType = 'quiz') {
                            try {
                                // Confirm with user before removing
                                if (!confirm('Are you sure you want to remove this notification?')) {
                                    return;
                                }

                                // Import deleteDoc function
                                const { deleteDoc, doc } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                                // Determine the collection based on notification type
                                const collectionName = notificationType === 'classCode' ? 'ClassCodeNotifications' : 'QuizNotifications';

                                // Delete the notification from Firestore
                                const notificationRef = doc(db, collectionName, notificationId);
                                await deleteDoc(notificationRef);

                                // Show success message
                                console.log('Notification removed successfully');

                                // Reload notifications to update the display
                                loadQuizNotifications();

                            } catch (error) {
                                console.error("Error removing notification:", error);
                                alert('Error removing notification. Please try again.');
                            }
                        }

                        // Function to mark all class code notifications as read
                        async function markAllClassCodeNotificationsAsRead() {
                            try {
                                const studentData = JSON.parse(localStorage.getItem('studentData'));
                                if (!studentData) {
                                    return;
                                }

                                // Get unread class code notifications for this student
                                const classCodeNotificationsRef = collection(db, "ClassCodeNotifications");
                                const classCodeNotificationQuery = query(
                                    classCodeNotificationsRef,
                                    where("studentEmail", "==", studentData.email),
                                    where("isRead", "==", false)
                                );

                                const classCodeNotificationSnapshot = await getDocs(classCodeNotificationQuery);

                                if (classCodeNotificationSnapshot.empty) {
                                    return;
                                }

                                // Update all class code notifications to read
                                const { updateDoc, doc } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");
                                const updatePromises = [];

                                classCodeNotificationSnapshot.forEach((notificationDoc) => {
                                    const notificationRef = doc(db, "ClassCodeNotifications", notificationDoc.id);
                                    updatePromises.push(updateDoc(notificationRef, { isRead: true }));
                                });

                                await Promise.all(updatePromises);

                                // Reload class code notifications to update the display
                                loadClassCodeNotifications();

                            } catch (error) {
                                console.error("Error marking class code notifications as read:", error);
                            }
                        }

                        // Function to toggle minimize/expand class code notifications section
                        function toggleClassCodeNotificationsMinimize() {
                            const container = document.getElementById('class-code-notifications-container');
                            const button = document.getElementById('minimize-class-notifications-btn');
                            const icon = button.querySelector('i');

                            if (container && button && icon) {
                                const isMinimized = container.style.display === 'none' || container.style.maxHeight === '0px';

                                if (isMinimized) {
                                    // Expand with smooth animation
                                    container.style.display = 'block';
                                    container.style.maxHeight = 'none';
                                    container.style.opacity = '1';
                                    icon.className = 'fas fa-chevron-up';
                                    button.title = 'Minimize section';
                                    localStorage.setItem('classCodeNotificationsMinimized', 'false');
                                } else {
                                    // Minimize with smooth animation
                                    container.style.maxHeight = '0px';
                                    container.style.opacity = '0';
                                    icon.className = 'fas fa-chevron-down';
                                    button.title = 'Expand section';
                                    localStorage.setItem('classCodeNotificationsMinimized', 'true');

                                    // Hide completely after animation
                                    setTimeout(() => {
                                        if (container.style.maxHeight === '0px') {
                                            container.style.display = 'none';
                                        }
                                    }, 300);
                                }
                            }
                        }

                        // Function to restore minimize state from localStorage
                        function restoreClassCodeNotificationsMinimizeState() {
                            const isMinimized = localStorage.getItem('classCodeNotificationsMinimized') === 'true';

                            if (isMinimized) {
                                const container = document.getElementById('class-code-notifications-container');
                                const button = document.getElementById('minimize-class-notifications-btn');
                                const icon = button?.querySelector('i');

                                if (container && button && icon) {
                                    container.style.display = 'none';
                                    container.style.maxHeight = '0px';
                                    container.style.opacity = '0';
                                    icon.className = 'fas fa-chevron-down';
                                    button.title = 'Expand section';
                                }
                            } else {
                                // Ensure expanded state is properly set
                                const container = document.getElementById('class-code-notifications-container');
                                const button = document.getElementById('minimize-class-notifications-btn');
                                const icon = button?.querySelector('i');

                                if (container && button && icon) {
                                    container.style.display = 'block';
                                    container.style.maxHeight = 'none';
                                    container.style.opacity = '1';
                                    icon.className = 'fas fa-chevron-up';
                                    button.title = 'Minimize section';
                                }
                            }
                        }

                        // Initialize the page
                        document.addEventListener('DOMContentLoaded', function() {
                            loadEnrolledClassrooms();
                            loadQuizScores();
                            loadQuizNotifications();
                            loadClassCodeNotifications(); // Load class code notifications

                            // Add event listener for back to classrooms button
                            const backButton = document.getElementById('back-to-classrooms');
                            if (backButton) {
                                backButton.addEventListener('click', function() {
                                    showMainClassroomList();
                                });
                            }

                            // Add event listener for clear notifications button
                            const clearNotificationsBtn = document.getElementById('clear-notifications-btn');
                            if (clearNotificationsBtn) {
                                clearNotificationsBtn.addEventListener('click', markAllNotificationsAsRead);
                            }

                            // Add event listener for clear class code notifications button
                            const clearClassNotificationsBtn = document.getElementById('clear-class-notifications-btn');
                            if (clearClassNotificationsBtn) {
                                clearClassNotificationsBtn.addEventListener('click', markAllClassCodeNotificationsAsRead);
                            }

                            // Add event listener for minimize class code notifications button
                            const minimizeClassNotificationsBtn = document.getElementById('minimize-class-notifications-btn');
                            if (minimizeClassNotificationsBtn) {
                                minimizeClassNotificationsBtn.addEventListener('click', toggleClassCodeNotificationsMinimize);
                            }

                            // Add event listeners for sorting and grouping controls
                            const groupSelect = document.getElementById('quiz-notifications-group-select');
                            const sortSelect = document.getElementById('quiz-notifications-sort-select');

                            if (groupSelect) {
                                groupSelect.addEventListener('change', function() {
                                    localStorage.setItem('quizNotificationsGroupPreference', this.value);
                                    loadQuizNotifications(); // Reload with new grouping
                                });

                                // Load saved preference
                                const savedGroupPreference = localStorage.getItem('quizNotificationsGroupPreference');
                                if (savedGroupPreference) {
                                    groupSelect.value = savedGroupPreference;
                                }
                            }

                            if (sortSelect) {
                                sortSelect.addEventListener('change', function() {
                                    localStorage.setItem('quizNotificationsSortPreference', this.value);
                                    loadQuizNotifications(); // Reload with new sorting
                                });

                                // Load saved preference
                                const savedSortPreference = localStorage.getItem('quizNotificationsSortPreference');
                                if (savedSortPreference) {
                                    sortSelect.value = savedSortPreference;
                                }
                            }
                        });
                    </script>










                </div>
                <!-- /.container-fluid -->


                <div class="container-fluid" id="quizzes-and-exams-container" style="display: none;">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Quizzes and Exams</h1>
                    </div>

                    <!-- Quiz Notifications Section -->
                    <div id="quiz-notifications-section" class="mb-4" style="display: none;">
                        <div class="d-sm-flex align-items-center justify-content-between mb-3">
                            <h4 class="h5 mb-0 text-gray-800">
                                <i class="fas fa-bell text-warning"></i> Quiz Notifications
                            </h4>
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-secondary mr-2" id="clear-notifications-btn">
                                    <i class="fas fa-check"></i> Mark All as Read
                                </button>
                            </div>
                        </div>

                        <!-- Sorting and Grouping Controls -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-0">
                                    <label for="quiz-notifications-group-select" class="small text-muted mb-1">Group by:</label>
                                    <select class="form-control form-control-sm" id="quiz-notifications-group-select">
                                        <option value="none">No Grouping</option>
                                        <option value="subject">Subject</option>
                                        <option value="grade">Grade Level</option>
                                        <option value="section">Section</option>
                                        <option value="course">Strand</option>
                                        <option value="status">Status (Read/Unread)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-0">
                                    <label for="quiz-notifications-sort-select" class="small text-muted mb-1">Sort by:</label>
                                    <select class="form-control form-control-sm" id="quiz-notifications-sort-select">
                                        <option value="date-newest">Date (Newest First)</option>
                                        <option value="date-oldest">Date (Oldest First)</option>
                                        <option value="name-asc">Quiz Name (A-Z)</option>
                                        <option value="name-desc">Quiz Name (Z-A)</option>
                                        <option value="subject-asc">Subject (A-Z)</option>
                                        <option value="subject-desc">Subject (Z-A)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="quiz-notifications-container">
                            <!-- Quiz notifications will be displayed here -->
                        </div>
                    </div>

                    <!-- Take Quiz Button -->
                    <button class="btn btn-primary mb-4" data-toggle="modal" data-target="#takeQuizModal">
                        <i class="fas fa-pen-to-square mr-2"></i>Take Quiz
                    </button>

                    <!-- Quiz Scores Section -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Your Quiz Scores</h6>
                        </div>
                        <div class="card-body">
                            <div id="quiz-scores-container">
                                <!-- Quiz scores will be displayed here -->
                            </div>
                        </div>
                    </div>

                    <!-- Quiz List Container -->
                    <div id="quiz-list-container" class="row">
                        <!-- Quizzes will be displayed here -->
                    </div>
                </div>

                <!-- Take Quiz Modal -->
                <div class="modal fade" id="takeQuizModal" tabindex="-1" role="dialog" aria-labelledby="takeQuizModalLabel" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="takeQuizModalLabel">Enter Quiz Code</h5>
                                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="quizCodeForm">
                                    <div class="form-group">
                                        <label for="quizCode">Quiz Code:</label>
                                        <input type="text" class="form-control" id="quizCode" placeholder="Enter quiz code" required>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                                <button class="btn btn-primary" type="button" id="submitQuizCode">Start Quiz</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; LEAD LMS Website 2025</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" id="logoutButton">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="js/sb-admin-2.min.js"></script>

    <!-- Page level plugins -->
    <script src="vendor/chart.js/Chart.min.js"></script>



    <!-- Custom CSS for lime green sidebar -->
    <style>
        /* Override sidebar background to lime green */
        .sidebar.bg-gradient-primary {
            background-color: #32CD32 !important;
            background-image: linear-gradient(180deg, #32CD32 10%, #228B22 100%) !important;
            background-size: cover;
        }

        /* Ensure sidebar brand and nav items have proper contrast */
        .sidebar.bg-gradient-primary .sidebar-brand {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link:hover,
        .sidebar.bg-gradient-primary .nav-item .nav-link:focus,
        .sidebar.bg-gradient-primary .nav-item.active .nav-link {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link i {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link:hover i,
        .sidebar.bg-gradient-primary .nav-item .nav-link:focus i,
        .sidebar.bg-gradient-primary .nav-item.active .nav-link i {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .sidebar-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.15) !important;
        }

        .sidebar.bg-gradient-primary #sidebarToggle {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }

        .sidebar.bg-gradient-primary #sidebarToggle:hover {
            background-color: rgba(255, 255, 255, 0.3) !important;
        }

        /* Make the dashboard logo more noticeable */
        .sidebar-brand-icon {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 50% !important;
            padding: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 2px 6px rgba(0, 0, 0, 0.2) !important;
            border: 2px solid rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .sidebar-brand-icon img {
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
        }

        /* Enhanced effects on hover */
        .sidebar-brand:hover .sidebar-brand-icon {
            background: rgba(255, 255, 255, 1) !important;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 3px 8px rgba(0, 0, 0, 0.3) !important;
            border: 2px solid #ffffff !important;
            transform: scale(1.05) !important;
        }

        .sidebar-brand:hover .sidebar-brand-icon img {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
        }
    </style>

    <style>
        .dropdown-menu {
            position: absolute;
            top: 30px;
            right: 10px;
            background: white;
            box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            z-index: 10;
        }

        .dropdown-menu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: black;
        }

        .dropdown-menu a:hover {
            background: #f1f1f1;
        }

        .d-none {
            display: none !important;
        }

        .clickable-card {
            cursor: pointer;
            transition: transform 0.2s ease-in-out;
        }

        .clickable-card:hover {
            transform: scale(1.03);
        }


        /* Three-dot menu (⋮) container */
        .three-dot-container {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        /* Three-dot button */
        .three-dot-btn {
            background: transparent;
            border: none;
            font-size: 16px;
            cursor: pointer;
            padding: 5px;
        }

        /* Style the dropdown menu */
        .dropdown-menu {
            position: absolute;
            top: 25px;
            right: 0;
            background: white;
            box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            z-index: 10;
            width: 120px;
            padding: 5px;
        }

        .dropdown-menu a {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: black;
            font-size: 14px;
        }

        .dropdown-menu a:hover {
            background: #f1f1f1;
        }

        /* Ensure dropdown is hidden initially */
        .d-none {
            display: none !important;
        }

        /* Make the card feel clickable */
        .clickable-card {
            cursor: pointer;
            transition: transform 0.2s ease-in-out;
        }

        .clickable-card:hover {
            transform: scale(1.03);
        }

        .student-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .subject-link {
            color: #4e73df;
            transition: color 0.3s ease;
        }

        .subject-link:hover {
            color: #2e59d9;
            text-decoration: none;
        }

        .unenroll-btn {
            color: #e74a3b;
            transition: color 0.3s ease;
        }

        .unenroll-btn:hover {
            color: #d52a1a;
        }

        .classroom-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #e3e6f0;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-weight: bold;
            color: #4e73df;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #858796;
        }

        /* Update the styles to match Firestore data structure */
        .card {
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            margin-bottom: 1rem;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
        }

        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }

        .text-primary {
            color: #4e73df !important;
        }

        .clickable-card {
            cursor: pointer;
        }

        /* Classroom Info Styles */
        .classroom-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.9rem;
            color: #5a5c69;
        }

        .info-item i {
            width: 20px;
            text-align: center;
            color: #4e73df;
        }

        /* Card Header Styles */
        .card-body {
            padding: 1.25rem;
        }

        .card-body .row {
            margin: 0;
        }

        .card-body .col-auto {
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            padding: 0;
            position: relative;
        }

        .card-body .col-auto i.fa-book {
            position: absolute;
            top: 2.5rem;
            right: 0.5rem;
            font-size: 2rem;
            color: #e3e6f0;
        }

        .dropdown {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            z-index: 2;
        }

        .card-body {
            position: relative;
            padding: 1.25rem;
        }

        .card-body .row {
            margin: 0;
            position: relative;
            min-height: 4rem;
        }

        /* Button Styles */
        .btn-link {
            color: #858796;
            text-decoration: none;
            background-color: transparent;
            border: 0;
            padding: 0.375rem 0.75rem;
        }

        .btn-link:hover {
            color: #4e73df;
            text-decoration: none;
        }

        /* Join Classroom Card Styles */
        #join-classroom-card {
            position: relative;
            z-index: 2;
            margin: 1rem 0;
        }

        #join-classroom-card .card-body {
            padding: 1.5rem;
        }

        #join-classroom-card .form-control {
            margin-bottom: 1rem;
        }

        #join-classroom-card .btn {
            margin: 0.25rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .card-body .col-auto {
                display: none;
            }

            .classroom-info {
                margin-top: 0.5rem;
            }
        }

        /* Dropdown Menu Styles */
        .dropdown {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            z-index: 2;
        }

        .dropdown-toggle::after {
            display: none;
        }

        .btn-link {
            color: #858796;
            text-decoration: none;
            background-color: transparent;
            border: 0;
            padding: 0.25rem;
        }

        .btn-link:hover {
            color: #4e73df;
            text-decoration: none;
        }

        /* Profile Dropdown Styles */
        .dropdown-header {
            padding: 1rem;
            text-align: center;
        }

        .profile-info {
            margin-top: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        /* Sidebar Toggle Hover Style */
        #sidebarToggle .nav-link:hover {
            color: #105c1c !important;
        }

        /* Profile Modal Styles */
        .modal-body {
            padding: 2rem;
        }

        .card {
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .modal-dialog {
                margin: 0.5rem;
            }
        }

        /* Add this to your existing styles */
        .sidebar.toggled .nav-text {
            display: none;
        }

        .sidebar.toggled .nav-link {
            text-align: center !important;
            padding: 0.75rem 1rem;
        }

        .sidebar.toggled .nav-link i {
            margin-right: 0;
        }

        .nav-link i {
            margin-right: 0.5rem;
        }

        /* Quiz Score Styles */
        .quiz-score-item {
            background-color: #f8f9fc;
            transition: transform 0.2s ease-in-out;
        }

        .quiz-score-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .badge {
            font-size: 0.9rem;
            padding: 0.5em 0.8em;
        }

        .badge-success {
            background-color: #1cc88a;
        }

        .badge-danger {
            background-color: #e74a3b;
        }

        /* Text overflow handling for classroom cards */
        .classroom-card-text {
            overflow: hidden;
            word-wrap: break-word;
            word-break: break-word;
            hyphens: auto;
        }

        .classroom-card-text.truncate {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .classroom-card-text.truncate-single {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Specific styles for different text elements */
        .subject-text {
            max-width: 100%;
        }

        .section-text {
            max-width: 100%;
        }

        .details-text {
            max-width: 100%;
            max-height: 3em;
            line-height: 1.5em;
        }

        /* Tooltip styles for showing full text */
        .text-tooltip {
            position: relative;
            cursor: help;
        }

        .text-tooltip:hover::after {
            content: attr(data-full-text);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.875rem;
            white-space: normal;
            max-width: 300px;
            word-wrap: break-word;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .text-tooltip:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(100%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        /* Ensure consistent card heights */
        .card.h-100 {
            min-height: 200px;
            max-height: 300px;
        }

        .card-body {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .col.mr-2 {
            flex: 1;
            min-width: 0; /* Allow flex item to shrink below content size */
        }

        @media (max-width: 768px) {
            .card.h-100 {
                min-height: 180px;
                max-height: 280px;
            }

            .text-tooltip:hover::after {
                max-width: 250px;
                font-size: 0.8rem;
            }
        }

        /* Quiz Notification Styles */
        .notification-card {
            border: 1px solid #e3e6f0;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .notification-card.unread {
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
        }

        .notification-card.read {
            border-left: 4px solid #6c757d;
            opacity: 0.85;
            background-color: #ffffff;
        }

        .notification-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            transform: translateY(-2px);
            border-color: #007bff;
        }

        .notification-card .card-body {
            padding: 1rem;
        }

        /* Classroom details styling */
        .notification-card .bg-light {
            background-color: #f1f3f4 !important;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
        }

        .notification-card .bg-light small {
            line-height: 1.4;
        }

        .notification-card .bg-light .fas {
            color: #6c757d;
            margin-right: 0.25rem;
        }

        .badge-warning {
            animation: pulse 2s infinite;
            background-color: #ffc107;
            color: #212529;
        }

        .badge-info {
            background-color: #17a2b8;
        }

        .badge-primary {
            background-color: #007bff;
        }

        /* Remove notification button styles */
        .remove-notification-btn {
            opacity: 0.6;
            transition: all 0.2s ease;
            font-size: 0.75rem;
            border: 1px solid #dc3545;
            background-color: transparent;
            color: #dc3545;
        }

        .remove-notification-btn:hover {
            opacity: 1;
            transform: scale(1.05);
            background-color: #dc3545;
            color: white;
        }

        .notification-card:hover .remove-notification-btn {
            opacity: 0.8;
        }

        /* Take quiz button styles */
        .take-quiz-btn {
            white-space: nowrap;
            font-size: 0.875rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }

        .take-quiz-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        #quiz-notifications-section {
            border-radius: 0.5rem;
            background-color: #ffffff;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e3e6f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #quiz-notifications-section h4 {
            color: #495057;
            font-weight: 600;
        }

        #quiz-notifications-section .fas.fa-bell {
            margin-right: 0.5rem;
        }

        /* Class Code Notifications Section Styling */
        #class-code-notifications-section {
            border-radius: 0.5rem;
            background-color: #ffffff;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e3e6f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        #class-code-notifications-section h4 {
            color: #495057;
            font-weight: 600;
        }

        #class-code-notifications-section .fas.fa-key {
            margin-right: 0.5rem;
        }

        /* Class Code Notifications Minimize Button */
        #minimize-class-notifications-btn {
            transition: all 0.2s ease;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }

        #minimize-class-notifications-btn:hover {
            background-color: #f8f9fa;
            border-color: #6c757d;
            transform: scale(1.05);
        }

        #minimize-class-notifications-btn i {
            transition: transform 0.2s ease;
        }

        /* Class Code Notifications Container Transition */
        #class-code-notifications-container {
            transition: max-height 0.3s ease, opacity 0.3s ease;
            overflow: hidden;
        }

        /* When minimized */
        #class-code-notifications-container.minimized {
            max-height: 0 !important;
            opacity: 0 !important;
        }

        /* Group header styling - matching Classes section */
        .group-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            margin: 20px 0 15px 0;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .group-header:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .group-header:first-child {
            margin-top: 0;
        }

        .group-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 8px 0 0 8px;
        }

        .group-header .group-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .group-header .group-count {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.85rem;
            margin-left: 10px;
        }

        .group-header .group-minimize-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            margin-left: 10px;
        }

        .group-header .group-minimize-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .group-content {
            transition: all 0.3s ease;
        }

        /* Copy Quiz Code Button Styles */
        .copy-quiz-code-btn {
            color: #6c757d;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .copy-quiz-code-btn:hover {
            color: #007bff;
            transform: scale(1.1);
        }

        .copy-quiz-code-btn:focus {
            outline: none;
            box-shadow: none;
        }

        /* Copy Class Code Button Styles */
        .copy-class-code-btn {
            color: #6c757d;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .copy-class-code-btn:hover {
            color: #28a745;
            transform: scale(1.1);
        }

        .copy-class-code-btn:focus {
            outline: none;
            box-shadow: none;
        }

        /* Join Class Button Styles */
        .join-class-btn {
            transition: all 0.2s ease;
        }

        .join-class-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .notification-card .d-flex.flex-column {
                flex-direction: row;
                gap: 0.5rem;
            }

            .notification-card .take-quiz-btn,
            .notification-card .remove-notification-btn {
                font-size: 0.75rem;
                padding: 0.375rem 0.5rem;
            }

            #quiz-notifications-section,
            #class-code-notifications-section {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .group-header {
                padding: 10px 15px;
                margin: 15px 0 10px 0;
                font-size: 1rem;
            }

            .group-header .group-count {
                font-size: 0.75rem;
                padding: 3px 6px;
                margin-left: 8px;
            }

            .group-header .group-minimize-btn {
                width: 28px;
                height: 28px;
                font-size: 12px;
                margin-left: 8px;
            }
        }
    </style>





</body>

</html>

<script>
    // Session timeout duration (30 minutes in milliseconds)
    const SESSION_TIMEOUT = 30 * 60 * 1000;
    let sessionTimer;

    // Function to reset the session timer
    function resetSessionTimer() {
        if (sessionTimer) {
            clearTimeout(sessionTimer);
        }
        sessionTimer = setTimeout(() => {
            console.log('Session timed out due to inactivity');
            handleSessionTimeout();
        }, SESSION_TIMEOUT);
    }

    // Function to handle session timeout
    function handleSessionTimeout() {
        try {
            // Clear localStorage
            localStorage.removeItem('studentData');
            localStorage.removeItem('loggedInUserId');
            localStorage.removeItem('enrolledClassrooms');
            // Alert the user
            alert('Your session has expired due to inactivity. Please log in again.');
            // Redirect to login page
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Error during session timeout:', error);
        }
    }

    // Add event listeners for user activity
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    activityEvents.forEach(event => {
        document.addEventListener(event, resetSessionTimer);
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Get student data from localStorage
        const studentData = JSON.parse(localStorage.getItem('studentData'));
        console.log('Student data from localStorage:', studentData);

        // Check if user is logged in
        if (!studentData) {
            // If no student data is found, redirect to login page
            alert('Please log in to access this page.');
            window.location.href = 'index.html';
            return;
        }

        // Start the session timer when the page loads
        resetSessionTimer();

        // Update the profile information
        // Update the profile dropdown with student information
        console.log('Setting userName to:', `${studentData.firstName} ${studentData.lastName}`);
        const userNameElement = document.getElementById('userName');
        console.log('userName element:', userNameElement);

        if (userNameElement) {
            userNameElement.textContent = `${studentData.firstName} ${studentData.lastName}`;
        }

        const profileNameElement = document.getElementById('profileName');
        if (profileNameElement) {
            profileNameElement.textContent = `${studentData.firstName} ${studentData.lastName}`;
        }

        const profileEmailElement = document.getElementById('profileEmail');
        if (profileEmailElement) {
            profileEmailElement.textContent = studentData.email;
        }

        // Update modal profile information
        const modalProfileNameElement = document.getElementById('modalProfileName');
        if (modalProfileNameElement) {
            modalProfileNameElement.textContent = `${studentData.firstName} ${studentData.lastName}`;
        }

        const modalProfileEmailElement = document.getElementById('modalProfileEmail');
        if (modalProfileEmailElement) {
            modalProfileEmailElement.textContent = studentData.email;
        }

        // Add logout functionality
        document.getElementById('logoutButton').addEventListener('click', function() {
            // Set flag to prevent beforeunload prompt
            isLoggingOut = true;
            // Clear the session timer
            if (sessionTimer) {
                clearTimeout(sessionTimer);
            }
            // Clear all relevant localStorage data
            localStorage.removeItem('studentData');
            localStorage.removeItem('loggedInUserId');
            localStorage.removeItem('enrolledClassrooms');

            // Redirect to index.html
            window.location.href = 'index.html';
        });

        // Add dashboard icon logout confirmation
        document.getElementById('dashboard-icon').addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default link behavior

            // Show confirmation dialog
            if (confirm('Navigating to the homepage will log you out of your current session. Do you want to proceed?')) {
                // Set flag to prevent beforeunload prompt
                isLoggingOut = true;
                // Clear the session timer
                if (sessionTimer) {
                    clearTimeout(sessionTimer);
                }
                // Clear all relevant localStorage data
                localStorage.removeItem('studentData');
                localStorage.removeItem('loggedInUserId');
                localStorage.removeItem('enrolledClassrooms');

                // Redirect to index.html
                window.location.href = 'index.html';
            }
        });

        // Flag to track intentional logout
        let isLoggingOut = false;
        let isNavigatingWithinLMS = false;

        // Track user interaction to enable beforeunload
        let userHasInteracted = false;
        const interactionEvents = ['click', 'keydown', 'keypress', 'mousedown', 'touchstart', 'input', 'change'];
        interactionEvents.forEach(eventType => {
            document.addEventListener(eventType, function() {
                userHasInteracted = true;
            }, { once: false, passive: true });
        });

        // Track internal LMS navigation to prevent logout prompts
        function markInternalNavigation() {
            isNavigatingWithinLMS = true;
            // Reset the flag after a short delay to handle page transitions
            setTimeout(() => {
                isNavigatingWithinLMS = false;
            }, 1000);
        }

        // Add event listeners to all internal LMS links and buttons
        document.addEventListener('click', function(e) {
            const target = e.target.closest('a, button, .clickable-card');
            if (target) {
                const href = target.href || target.getAttribute('data-href');
                const onclick = target.getAttribute('onclick');

                // Check if it's internal LMS navigation
                if (href && (
                    href.includes('Student11.html') ||
                    href.includes('index.html') ||
                    href.startsWith('#') ||
                    href.startsWith('javascript:')
                )) {
                    markInternalNavigation();
                } else if (onclick || target.classList.contains('clickable-card')) {
                    // Handle classroom cards and other interactive elements
                    markInternalNavigation();
                }
            }
        });



        // Add pagehide event to handle logout when page is actually being closed
        // This is more reliable than unload for cleanup operations
        window.addEventListener('pagehide', function(e) {
            // Only clear localStorage if we're not navigating within the LMS
            if (!isNavigatingWithinLMS && !isLoggingOut) {
                // Check if this is actually a tab/window close vs navigation
                // Don't clear data if we're just navigating to another page
                const isActuallyClosing = e.persisted === false &&
                                         !window.location.href.includes('Student11.html');

                if (isActuallyClosing) {
                    // Clear localStorage when page is actually closing
                    localStorage.removeItem('studentData');
                    localStorage.removeItem('loggedInUserId');
                    localStorage.removeItem('enrolledClassrooms');

                    // Try to sign out if possible (may not complete due to page unloading)
                    if (window.auth && typeof window.auth.signOut === 'function') {
                        try {
                            window.auth.signOut().catch(() => {
                                // Ignore errors during page unload
                            });
                        } catch (error) {
                            // Ignore errors during page unload
                        }
                    }
                }
            }
        });

        // Add unload event as fallback for older browsers
        window.addEventListener('unload', function(e) {
            // Clear localStorage when page is actually closing
            localStorage.removeItem('studentData');
            localStorage.removeItem('loggedInUserId');
            localStorage.removeItem('enrolledClassrooms');
        });
        // Add click handlers for the navigation buttons
        const viewModulesBtn = document.getElementById('view-modules-btn');
        const quizzesExamsBtn = document.getElementById('quizzes-exams-btn');
        const modulesContainer = document.getElementById('modules-container');
        const quizzesExamsContainer = document.getElementById('quizzes-and-exams-container');

        // Function to update active state
        function updateActiveState(activeBtn) {
            // Remove active class from both buttons
            viewModulesBtn.classList.remove('active');
            quizzesExamsBtn.classList.remove('active');
            // Add active class to clicked button
            activeBtn.classList.add('active');
        }

        viewModulesBtn.addEventListener('click', function() {
            modulesContainer.style.display = 'block';
            quizzesExamsContainer.style.display = 'none';
            updateActiveState(this);
        });

        quizzesExamsBtn.addEventListener('click', function() {
            modulesContainer.style.display = 'none';
            quizzesExamsContainer.style.display = 'block';
            updateActiveState(this);
            // Load quiz notifications when switching to Quizzes and Exams section
            loadQuizNotifications();
        });

        // Set initial state
        updateActiveState(viewModulesBtn);
    });
</script>

<!-- Profile Modal -->
<div class="modal fade" id="profileModal" tabindex="-1" role="dialog" aria-labelledby="profileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileModalLabel">Student Profile</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <p class="mb-0">Full Name</p>
                                    </div>
                                    <div class="col-sm-9">
                                        <p class="text-muted mb-0" id="modalProfileName"></p>
                                    </div>
                                </div>
                                <hr>
                                <div class="row mb-3">
                                    <div class="col-sm-3">
                                        <p class="mb-0">Email</p>
                                    </div>
                                    <div class="col-sm-9">
                                        <p class="text-muted mb-0" id="modalProfileEmail"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-dismiss="modal">Return</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Add these styles to your existing styles */
    .nav-link.active {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: #fff !important;
    }

    .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
</style>